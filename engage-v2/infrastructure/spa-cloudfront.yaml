AWSTemplateFormatVersion: '2010-09-09'
Description: 'CloudFront distribution for Engage V2 SPA with S3 origin'

Parameters:
  BucketName:
    Type: String
    Default: engage-v2-ui-bucket
    Description: Name of the S3 bucket for hosting the SPA
  
  DomainName:
    Type: String
    Default: ""
    Description: Custom domain name (optional)
  
  CertificateArn:
    Type: String
    Default: ""
    Description: ACM certificate ARN for custom domain (optional)

  ApiDomain:
    Type: String
    Default: api.engage-v2.example.com
    Description: API domain for proxying /api/* requests

Conditions:
  HasCustomDomain: !Not [!Equals [!Ref DomainName, ""]]
  HasCertificate: !Not [!Equals [!Ref CertificateArn, ""]]

Resources:
  # S3 Bucket for hosting the SPA
  S3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketName: !Ref BucketName
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      VersioningConfiguration:
        Status: Enabled

  # Origin Access Identity for CloudFront
  OriginAccessIdentity:
    Type: AWS::CloudFront::OriginAccessIdentity
    Properties:
      OriginAccessIdentityConfig:
        Comment: !Sub 'OAI for ${BucketName}'

  # Bucket Policy to allow CloudFront access
  BucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket: !Ref S3Bucket
      PolicyDocument:
        Statement:
          - Sid: AllowCloudFrontAccess
            Effect: Allow
            Principal:
              AWS: !Sub 'arn:aws:iam::cloudfront:user/CloudFront Origin Access Identity ${OriginAccessIdentity}'
            Action: 's3:GetObject'
            Resource: !Sub '${S3Bucket}/*'

  # CloudFront Distribution with SPA support
  CloudFrontDistribution:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Enabled: true
        Comment: !Sub 'CloudFront distribution for ${BucketName} SPA'
        DefaultRootObject: index.html
        
        # Origins
        Origins:
          - Id: S3Origin
            DomainName: !GetAtt S3Bucket.RegionalDomainName
            S3OriginConfig:
              OriginAccessIdentity: !Sub 'origin-access-identity/cloudfront/${OriginAccessIdentity}'
          - Id: ApiOrigin
            DomainName: !Ref ApiDomain
            CustomOriginConfig:
              HTTPPort: 443
              HTTPSPort: 443
              OriginProtocolPolicy: https-only

        # Default behavior for SPA
        DefaultCacheBehavior:
          TargetOriginId: S3Origin
          ViewerProtocolPolicy: redirect-to-https
          AllowedMethods:
            - GET
            - HEAD
            - OPTIONS
          CachedMethods:
            - GET
            - HEAD
            - OPTIONS
          Compress: true
          CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad # CachingOptimized

        # API behavior
        CacheBehaviors:
          - PathPattern: '/api/*'
            TargetOriginId: ApiOrigin
            ViewerProtocolPolicy: redirect-to-https
            AllowedMethods:
              - DELETE
              - GET
              - HEAD
              - OPTIONS
              - PATCH
              - POST
              - PUT
            CachedMethods:
              - GET
              - HEAD
            Compress: true
            CachePolicyId: 4135ea2d-6df8-44a3-9df3-4b5a84be39ad # CachingOptimized
            OriginRequestPolicyId: 88a5eaf4-2fd4-4709-b370-b4c650ea3fcf # CORS-S3Origin

        # SPA Error Pages - This is the key configuration!
        CustomErrorResponses:
          - ErrorCode: 403
            ResponseCode: 200
            ResponsePagePath: '/index.html'
            ErrorCachingMinTTL: 300
          - ErrorCode: 404
            ResponseCode: 200
            ResponsePagePath: '/index.html'
            ErrorCachingMinTTL: 300

        # Custom domain configuration
        Aliases: !If
          - HasCustomDomain
          - [!Ref DomainName]
          - !Ref AWS::NoValue
        
        ViewerCertificate: !If
          - HasCertificate
          - AcmCertificateArn: !Ref CertificateArn
            SslSupportMethod: sni-only
            MinimumProtocolVersion: TLSv1.2_2021
          - CloudFrontDefaultCertificate: true

        PriceClass: PriceClass_100

Outputs:
  BucketName:
    Description: 'S3 Bucket name'
    Value: !Ref S3Bucket
    Export:
      Name: !Sub '${AWS::StackName}-BucketName'

  DistributionId:
    Description: 'CloudFront Distribution ID'
    Value: !Ref CloudFrontDistribution
    Export:
      Name: !Sub '${AWS::StackName}-DistributionId'

  DistributionDomainName:
    Description: 'CloudFront Distribution Domain Name'
    Value: !GetAtt CloudFrontDistribution.DomainName
    Export:
      Name: !Sub '${AWS::StackName}-DistributionDomainName'

  WebsiteURL:
    Description: 'Website URL'
    Value: !If
      - HasCustomDomain
      - !Sub 'https://${DomainName}'
      - !Sub 'https://${CloudFrontDistribution.DomainName}'
    Export:
      Name: !Sub '${AWS::StackName}-WebsiteURL'
