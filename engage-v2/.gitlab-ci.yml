stages:
  - dependencies
  - lint
  - build
  - release

variables:
  GOLANG_VERSION: "1.24"
  GOLANGCI_LINT_VERSION: "v1.64.5"
  NODE_VERSION: "22.13.1"
  # Docker related vars
  DOCKER_DRIVER: overlay2
  DOCKER_HOST: tcp://docker:2376
  DOCKER_TLS_CERTDIR: "/certs"
  DOCKER_TLS_VERIFY: 1
  DOCKER_CERT_PATH: "$DOCKER_TLS_CERTDIR/client"
services:
  - docker:25.0-dind

# Checking outdated dependencies of Go project in the `api` folder
# Official go-mod-outdated guide: https://github.com/psampaz/go-mod-outdated?tab=readme-ov-file#ci-pipelines
dependencies_api:
  stage: dependencies
  image: golang:$GOLANG_VERSION
  before_script:
    - cd api
    - go mod tidy
    - go mod download
    - go install github.com/psampaz/go-mod-outdated@latest
  script:
    - go list -u -m -json all | go-mod-outdated -direct -ci
  only:
    - merge_requests
  allow_failure: false

# Checking outdated dependencies of Vite/React Typescript project in the `ui` folder
# Official pnpm guide: https://pnpm.io/cli/outdated
# Checking vulnerable dependencies of Vite/React Typescript project in the `ui` folder
# Official pnpm guide: https://pnpm.io/cli/audit/
dependencies_ui:
  stage: dependencies
  image: node:$NODE_VERSION
  before_script:
    - corepack enable
    - corepack prepare pnpm@10.0.0 --activate
    - pnpm config set store-dir .pnpm-store
  script:
    - cd ui
    - pnpm install
    - pnpm outdated
    - pnpm audit
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  only:
    - merge_requests
  allow_failure: false

# Linting Go project in the `api` folder
# Official golangci-lint guide: https://golangci-lint.run/welcome/install/#other-ci
# Official GitLab guide: https://docs.gitlab.com/ee/development/go_guide/#automatic-linting
lint_api:
  stage: lint
  image: golangci/golangci-lint:$GOLANGCI_LINT_VERSION
  script:
    # Write the code coverage report to gl-code-quality-report.json
    # and print linting issues to stdout in the format: path/to/file:line description
    # remove `--issues-exit-code 0` or set to non-zero to fail the job if linting issues are detected
    - cd api
    - golangci-lint run --timeout 10m --print-issued-lines=false --out-format code-climate:gl-code-quality-report.json,line-number
  artifacts:
    reports:
      codequality: gl-code-quality-report.json
    paths:
      - gl-code-quality-report.json
  only:
    - merge_requests
  allow_failure: false

# Linting Vite/React Typescript project in the `ui` folder
# Official pnpm guide: https://pnpm.io/continuous-integration#gitlab-ci
lint_ui:
  stage: lint
  image: node:$NODE_VERSION
  before_script:
    - corepack enable
    - corepack prepare pnpm@10.0.0 --activate
    - pnpm config set store-dir .pnpm-store
  script:
    - cd ui
    - pnpm install
    - pnpm run lint
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  only:
    - merge_requests
  allow_failure: true

# Build Go binary
build_api:
  stage: build
  image: golang:$GOLANG_VERSION
  script:
    - cd api
    - make build
  artifacts:
    paths:
      - api/engage-api
  only:
    - merge_requests
  allow_failure: false

# Build Vite project
build_ui:
  stage: build
  image: node:$NODE_VERSION
  before_script:
    - corepack enable
    - corepack prepare pnpm@10.0.0 --activate
    - pnpm config set store-dir .pnpm-store
  script:
    - cd ui
    - pnpm install
    - pnpm build
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  artifacts:
    paths:
      - ui/build
  only:
    - merge_requests
  allow_failure: false

# Build Vite project
build_ui_tagged:
  stage: build
  image: node:$NODE_VERSION
  before_script:
    - corepack enable
    - corepack prepare pnpm@10.0.0 --activate
    - pnpm config set store-dir .pnpm-store
  script:
    - cd ui
    - pnpm install
    - pnpm build
  cache:
    key:
      files:
        - pnpm-lock.yaml
    paths:
      - .pnpm-store
  artifacts:
    paths:
      - ui/build
  only:
    - tags
  except:
    - branches
  allow_failure: false

################################################
## TEMP RELEASE STAGE
## Due to the CDI Cache issue in GL, we need
## to build the UI in the same job we release it
################################################

release_ui:
  stage: release
  # Need to use the alpine version of the image to pull the release-cli tool
  image: node:${NODE_VERSION}-alpine
  variables:
    PACKAGE_REGISTRY_URL: "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/packages/generic/ui/${CI_COMMIT_TAG}"
    # Set the skelly envs
    VITE_AZURE_CLIENT_ID: xxx-xxx-xxx-xxx-xxx
    VITE_AZURE_AUTHORITY: yyy-yyy-yyy-yyy-yyy
    VITE_AZURE_API_SCOPE: api://zzz-zzz-zzz-zzz/user_impersonation
    VITE_POST_LOGOUT_REDIRECT_URI: https://myfirst.logout.domain
    VITE_BASE_URL_API: https://myfirst.base.url.domain

  dependencies:
    - build_ui_tagged
  before_script:
    - apk add curl zip
    - echo "@edge http://dl-cdn.alpinelinux.org/alpine/edge/community" >> /etc/apk/repositories
    - apk add --no-cache glab@edge
    # Build actions
    - corepack enable
    - corepack prepare pnpm@10.0.0 --activate
    - pnpm config set store-dir .pnpm-store
    - glab auth login --hostname $CI_SERVER_HOST --token $PROJECT_RELEASE_TOKEN
  only:
    - tags
  except:
    - branches
  script:
    # Build actions
    - cd ui
    - env > .env
    - pnpm install
    - pnpm build
    - cd ..
    # That'll do pig. That'll do.
    - glab release create ${CI_COMMIT_TAG}
    - cd ui/dist
    - zip -r engage_ui.zip *
    # Need to use the packages feature as you can't direct download via the normal /uploads/ GL feature
    - |
      curl --header "JOB-TOKEN: ${CI_JOB_TOKEN}" --upload-file engage_ui.zip ${PACKAGE_REGISTRY_URL}/engage_ui.zip
      assets="{\"name\": \"engage_ui.zip\",\"url\":\"${PACKAGE_REGISTRY_URL}/engage_ui.zip\",\"link_type\": \"package\"}"
      glab release upload ${CI_COMMIT_TAG} --assets-links="[${assets}]"
    - |
      for f in $(find . -type f ! -name '*.zip'); do 
      echo Uploading ${f:2}
      curl --header "JOB-TOKEN: ${CI_JOB_TOKEN}" --upload-file ${f} ${PACKAGE_REGISTRY_URL}/${f:2}
      glab release upload ${CI_COMMIT_TAG} --assets-links="[{\"name\": \"${f:2}\",\"url\":\"${PACKAGE_REGISTRY_URL}/${f:2}\",\"link_type\": \"other\"}]"
      done

################################################
## This should be used when CDI fix the cache FS issue (ticket number: CDI-10380)
################################################
# release_ui:
#   stage: release
#   image: registry.gitlab.com/gitlab-org/release-cli:latest
#   dependencies:
#     - build_ui_tagged
#   before_script:
#     - apk add curl zip
#   only:
#     - tags
#   except:
#     - branches
#   script:
#     # That'll do pig. That'll do.
#     - |
#       cd ui/build
#       zip -r engage_ui_${CI_COMMIT_TAG}.zip *
#       cd ../..
#     - | 
#       curl --header "JOB-TOKEN: ${CI_JOB_TOKEN}" --upload-file ui/build/engage_ui_${CI_COMMIT_TAG}.zip ${PACKAGE_REGISTRY_URL}/engage_ui_${CI_COMMIT_TAG}.zip
#     - |
#       release-cli create --name "Release $CI_COMMIT_TAG" \
#         --tag-name $CI_COMMIT_TAG \
#         --assets-link "{\"name\":\"engage_ui_${CI_COMMIT_TAG}.zip\",\"url\":\"${PACKAGE_REGISTRY_URL}/engage_ui_${CI_COMMIT_TAG}.zip\", \"link_type\": \"package\"}"


release_api:
  stage: release
  image: docker:25.0
  variables:
    MAIN_TAG: latest
    COMPONENT: api
    BUILD_PATH: "./api"
  # The following only and except means release will only be done on the main branch
  # when a tag is created. Aka - new version release
  only:
    - tags
  except:
    - branches
  before_script:
    - until docker info; do sleep 1; done
  script:
    - echo "Building GitLab image..."
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker build --cache-from $CI_REGISTRY_IMAGE/$COMPONENT:$CI_COMMIT_TAG -t $CI_REGISTRY_IMAGE/$COMPONENT:$CI_COMMIT_TAG $BUILD_PATH
    - docker push $CI_REGISTRY_IMAGE/$COMPONENT:$CI_COMMIT_TAG