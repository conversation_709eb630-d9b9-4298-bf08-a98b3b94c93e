package rabbitmq

import (
	"context"
	"fmt"
	"log/slog"
	"sync"
	"time"

	amqp "github.com/rabbitmq/amqp091-go"
)

// ConnectionManager manages RabbitMQ connections with automatic reconnection
type ConnectionManager struct {
	uri           string
	config        amqp.Config
	logger        *slog.Logger
	conn          *amqp.Connection
	channel       *amqp.Channel
	mutex         sync.RWMutex
	reconnecting  bool
	reconnectMux  sync.Mutex
	closed        bool
	closeChan     chan struct{}
	
	// Connection monitoring
	connCloseChan chan *amqp.Error
	chanCloseChan chan *amqp.Error
	
	// Reconnection settings
	maxReconnectAttempts int
	reconnectDelay       time.Duration
	maxReconnectDelay    time.Duration
}

// NewConnectionManager creates a new RabbitMQ connection manager
func NewConnectionManager(uri string, logger *slog.Logger) *ConnectionManager {
	return &ConnectionManager{
		uri: uri,
		config: amqp.Config{
			Heartbeat: 10 * time.Second,
			Locale:    "en_US",
		},
		logger:               logger,
		close<PERSON>han:            make(chan struct{}),
		maxReconnectAttempts: 10,
		reconnectDelay:       1 * time.Second,
		maxReconnectDelay:    30 * time.Second,
	}
}

// Connect establishes the initial connection
func (cm *ConnectionManager) Connect() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	if cm.closed {
		return fmt.Errorf("connection manager is closed")
	}
	
	return cm.connect()
}

// connect performs the actual connection (must be called with mutex held)
func (cm *ConnectionManager) connect() error {
	var err error
	
	// Close existing connections if any
	cm.closeConnections()
	
	// Establish connection
	cm.conn, err = amqp.DialConfig(cm.uri, cm.config)
	if err != nil {
		return fmt.Errorf("failed to connect to RabbitMQ: %w", err)
	}
	
	// Create channel
	cm.channel, err = cm.conn.Channel()
	if err != nil {
		cm.conn.Close()
		return fmt.Errorf("failed to open channel: %w", err)
	}
	
	// Set up connection monitoring
	cm.connCloseChan = make(chan *amqp.Error, 1)
	cm.chanCloseChan = make(chan *amqp.Error, 1)
	
	cm.conn.NotifyClose(cm.connCloseChan)
	cm.channel.NotifyClose(cm.chanCloseChan)
	
	cm.logger.Info("RabbitMQ connection established")
	
	// Start monitoring goroutine
	go cm.monitor()
	
	return nil
}

// closeConnections closes existing connections (must be called with mutex held)
func (cm *ConnectionManager) closeConnections() {
	if cm.channel != nil {
		cm.channel.Close()
		cm.channel = nil
	}
	if cm.conn != nil {
		cm.conn.Close()
		cm.conn = nil
	}
}

// monitor watches for connection/channel closures and triggers reconnection
func (cm *ConnectionManager) monitor() {
	for {
		select {
		case <-cm.closeChan:
			return
		case err := <-cm.connCloseChan:
			if err != nil {
				cm.logger.Error("RabbitMQ connection closed", "error", err.Error())
				cm.reconnect()
			}
			return
		case err := <-cm.chanCloseChan:
			if err != nil {
				cm.logger.Error("RabbitMQ channel closed", "error", err.Error())
				cm.reconnect()
			}
			return
		}
	}
}

// reconnect attempts to reconnect with exponential backoff
func (cm *ConnectionManager) reconnect() {
	cm.reconnectMux.Lock()
	defer cm.reconnectMux.Unlock()
	
	if cm.reconnecting || cm.closed {
		return
	}
	
	cm.reconnecting = true
	defer func() { cm.reconnecting = false }()
	
	delay := cm.reconnectDelay
	
	for attempt := 1; attempt <= cm.maxReconnectAttempts; attempt++ {
		select {
		case <-cm.closeChan:
			return
		default:
		}
		
		cm.logger.Info("Attempting to reconnect to RabbitMQ", 
			"attempt", attempt, 
			"max_attempts", cm.maxReconnectAttempts,
			"delay", delay)
		
		time.Sleep(delay)
		
		cm.mutex.Lock()
		err := cm.connect()
		cm.mutex.Unlock()
		
		if err == nil {
			cm.logger.Info("Successfully reconnected to RabbitMQ", "attempt", attempt)
			return
		}
		
		cm.logger.Error("Failed to reconnect to RabbitMQ", 
			"attempt", attempt, 
			"error", err.Error())
		
		// Exponential backoff with jitter
		delay = delay * 2
		if delay > cm.maxReconnectDelay {
			delay = cm.maxReconnectDelay
		}
	}
	
	cm.logger.Error("Failed to reconnect to RabbitMQ after maximum attempts", 
		"max_attempts", cm.maxReconnectAttempts)
}

// GetChannel returns the current channel, attempting reconnection if needed
func (cm *ConnectionManager) GetChannel() (*amqp.Channel, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	if cm.closed {
		return nil, fmt.Errorf("connection manager is closed")
	}
	
	if cm.channel == nil || cm.channel.IsClosed() {
		return nil, fmt.Errorf("channel is not available")
	}
	
	return cm.channel, nil
}

// IsConnected returns true if the connection and channel are healthy
func (cm *ConnectionManager) IsConnected() bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	
	return cm.conn != nil && !cm.conn.IsClosed() && 
		   cm.channel != nil && !cm.channel.IsClosed()
}

// Close closes the connection manager
func (cm *ConnectionManager) Close() error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()
	
	if cm.closed {
		return nil
	}
	
	cm.closed = true
	close(cm.closeChan)
	
	cm.closeConnections()
	
	cm.logger.Info("RabbitMQ connection manager closed")
	return nil
}

// PublishWithRetry publishes a message with automatic retry on connection failure
func (cm *ConnectionManager) PublishWithRetry(ctx context.Context, exchange, key string, mandatory, immediate bool, msg amqp.Publishing) error {
	maxRetries := 3
	
	for attempt := 1; attempt <= maxRetries; attempt++ {
		ch, err := cm.GetChannel()
		if err != nil {
			if attempt == maxRetries {
				return fmt.Errorf("failed to get channel after %d attempts: %w", maxRetries, err)
			}
			time.Sleep(time.Duration(attempt) * time.Second)
			continue
		}
		
		err = ch.PublishWithContext(ctx, exchange, key, mandatory, immediate, msg)
		if err == nil {
			return nil
		}
		
		// Check if it's a connection error
		if isConnectionError(err) {
			cm.logger.Warn("Publish failed due to connection error, retrying", 
				"attempt", attempt, 
				"error", err.Error())
			
			if attempt < maxRetries {
				time.Sleep(time.Duration(attempt) * time.Second)
				continue
			}
		}
		
		return fmt.Errorf("failed to publish message: %w", err)
	}
	
	return fmt.Errorf("failed to publish message after %d attempts", maxRetries)
}

// isConnectionError checks if the error is related to connection issues
func isConnectionError(err error) bool {
	if err == nil {
		return false
	}
	
	errStr := err.Error()
	return contains(errStr, "channel/connection is not open") ||
		   contains(errStr, "connection closed") ||
		   contains(errStr, "channel closed") ||
		   contains(errStr, "broken pipe") ||
		   contains(errStr, "connection reset")
}

// contains is a simple string contains check
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(s) > len(substr) && 
		 (s[:len(substr)] == substr || 
		  s[len(s)-len(substr):] == substr || 
		  containsSubstring(s, substr))))
}

func containsSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
