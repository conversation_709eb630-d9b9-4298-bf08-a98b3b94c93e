package validation

import (
	"fmt"
	"regexp"
	"strings"
)

// SanitizeInstanceType sanitizes instance type strings to prevent XSS
func SanitizeInstanceType(instanceType string) string {
	if instanceType == "" {
		return ""
	}

	// Remove any characters that aren't alphanumeric, dots, hyphens, or underscores
	reg := regexp.MustCompile(`[^a-zA-Z0-9.\-_]`)
	sanitized := reg.ReplaceAllString(instanceType, "")

	// Limit length to prevent abuse
	if len(sanitized) > 50 {
		sanitized = sanitized[:50]
	}

	return sanitized
}

// ValidateInstanceType validates instance type format and content
func ValidateInstanceType(instanceType string) error {
	if instanceType == "" {
		return fmt.Errorf("instance type cannot be empty")
	}

	// Check length
	if len(instanceType) > 50 {
		return fmt.Errorf("instance type too long (max 50 characters)")
	}

	// Check for valid characters only
	validPattern := regexp.MustCompile(`^[a-zA-Z0-9.\-_]+$`)
	if !validPattern.MatchString(instanceType) {
		return fmt.Errorf("instance type contains invalid characters (only alphanumeric, dots, hyphens, and underscores allowed)")
	}

	// Check for potential XSS patterns
	if containsXSSPatterns(instanceType) {
		return fmt.Errorf("instance type contains potentially malicious content")
	}

	return nil
}

// containsXSSPatterns checks for common XSS attack patterns
func containsXSSPatterns(input string) bool {
	// Convert to lowercase for case-insensitive matching
	lower := strings.ToLower(input)

	// Common XSS patterns
	xssPatterns := []string{
		"<script",
		"javascript:",
		"on",
		"<iframe",
		"<object",
		"<embed",
		"<link",
		"<meta",
		"data:text/html",
		"vbscript:",
		"expression(",
		"eval(",
		"alert(",
		"confirm(",
		"prompt(",
	}

	for _, pattern := range xssPatterns {
		if strings.Contains(lower, pattern) {
			return true
		}
	}

	return false
}

// SanitizeUserInput sanitizes general user input
func SanitizeUserInput(input string, maxLength int) string {
	if input == "" {
		return ""
	}

	// Remove HTML tags and potentially dangerous characters
	reg := regexp.MustCompile(`<[^>]*>`)
	sanitized := reg.ReplaceAllString(input, "")

	// Remove script-related content
	scriptReg := regexp.MustCompile(`(?i)(javascript:|data:text/html|vbscript:|on\w+\s*=)`)
	sanitized = scriptReg.ReplaceAllString(sanitized, "")

	// Limit length
	if len(sanitized) > maxLength {
		sanitized = sanitized[:maxLength]
	}

	return strings.TrimSpace(sanitized)
}

// ValidateCloudInstanceName validates cloud instance names
func ValidateCloudInstanceName(name string) error {
	if name == "" {
		return fmt.Errorf("instance name cannot be empty")
	}

	if len(name) > 256 {
		return fmt.Errorf("instance name too long (max 256 characters)")
	}

	// AWS instance name pattern (from existing validation)
	validPattern := regexp.MustCompile(`^[a-zA-Z0-9 _.:/=+\-@]{1,256}$`)
	if !validPattern.MatchString(name) {
		return fmt.Errorf("instance name contains invalid characters")
	}

	// Check for XSS patterns
	if containsXSSPatterns(name) {
		return fmt.Errorf("instance name contains potentially malicious content")
	}

	return nil
}
