package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/danielgtaylor/huma/v2/adapters/humachi"
	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/joho/godotenv"

	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/accounts"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/azure"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/rabbitmq"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/scheduler"
	awsScheduler "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/scheduler/aws"
	azureScheduler "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/scheduler/azure"
)

// application contains state that will be used across the server and will be passed down to lower layers:
// database connection, structured logger, database queries, and Terraform execution path
type application struct {
	secretKey            string
	dbConn               *pgxpool.Pool
	rabbitMqManager      *rabbitmq.ConnectionManager
	logger               *slog.Logger
	queries              *db.Queries
	terraformExecPath    string
	graphClient          *azure.GraphClient
	standardUsersGroupID string
	adminUsersGroupID    string
	awsRootRegion        string
	awsRootEmail         string
}

// getChannel safely gets a RabbitMQ channel from the connection manager
func (app *application) getChannel() (*amqp.Channel, error) {
	return app.rabbitMqManager.GetChannel()
}

func LoadENV() {
	path := ".env"
	for {
		err := godotenv.Load(path)
		if err == nil {
			break
		}
		path = "../" + path
	}
}

func InitApplication() (*application, map[string]string, error) {
	// Structured logging
	logger := slog.New(slog.NewTextHandler(os.Stdout, nil))

	// Environment variables
	LoadENV()

	env := map[string]string{}
	env["SECRET_KEY"] = os.Getenv("SECRET_KEY")
	env["AZURE_TENANT_ID"] = os.Getenv("AZURE_TENANT_ID")
	env["AZURE_APP_ID"] = os.Getenv("AZURE_APP_ID")
	env["AZURE_CLIENT_SECRET"] = os.Getenv("AZURE_CLIENT_SECRET")
	env["STANDARD_USERS_GROUP_ID"] = os.Getenv("STANDARD_USERS_GROUP_ID")
	env["ADMIN_USERS_GROUP_ID"] = os.Getenv("ADMIN_USERS_GROUP_ID")
	env["CORS_URL"] = os.Getenv("CORS_URL")
	env["DB_USER"] = os.Getenv("DB_USER")
	env["DB_PASSWORD"] = os.Getenv("DB_PASSWORD")
	env["DB_HOST"] = os.Getenv("DB_HOST")
	env["DB_PORT"] = os.Getenv("DB_PORT")
	env["DB_NAME"] = os.Getenv("DB_NAME")
	env["RABBITMQ_USER"] = os.Getenv("RABBITMQ_USER")
	env["RABBITMQ_PASSWORD"] = os.Getenv("RABBITMQ_PASSWORD")
	env["RABBITMQ_HOST"] = os.Getenv("RABBITMQ_HOST")
	env["RABBITMQ_PORT"] = os.Getenv("RABBITMQ_PORT")
	env["RABBITMQ_PROTO"] = os.Getenv("RABBITMQ_PROTO")
	env["TERRAFORM_EXEC_PATH"] = os.Getenv("TERRAFORM_EXEC_PATH")
	env["AWS_ROOT_REGION"] = os.Getenv("AWS_ROOT_REGION")
	env["AWS_ROOT_EMAIL"] = os.Getenv("AWS_ROOT_EMAIL")

	// RabbitMQ connection
	uri := fmt.Sprintf("%s://%s:%s@%s:%s/",
		env["RABBITMQ_PROTO"],
		env["RABBITMQ_USER"],
		env["RABBITMQ_PASSWORD"],
		env["RABBITMQ_HOST"],
		env["RABBITMQ_PORT"],
	)

	rabbitMqManager := rabbitmq.NewConnectionManager(uri, logger)
	err := rabbitMqManager.Connect()
	if err != nil {
		log.Fatalf("RabbitMQ connection failed: %v", err)
	}

	logger.Info("RabbitMQ connected",
		"host", env["RABBITMQ_HOST"],
		"port", env["RABBITMQ_PORT"],
	)

	// Database connection
	ctx := context.Background()
	dbConn, err := pgxpool.New(ctx, fmt.Sprintf("postgres://%s:%s@%s:%s/%s", env["DB_USER"], env["DB_PASSWORD"], env["DB_HOST"], env["DB_PORT"], env["DB_NAME"]))
	if err != nil {
		log.Fatalf("failed to connect to the database: %s", err.Error())
	}

	queries := db.New(dbConn)

	logger.Info("Connected to database", "host", env["DB_HOST"], "port", env["DB_PORT"], "name", env["DB_NAME"])

	graphClient := azure.NewGraphClient(env["AZURE_APP_ID"], env["AZURE_CLIENT_SECRET"], env["AZURE_TENANT_ID"])

	// Application context
	app := &application{
		secretKey:            env["SECRET_KEY"],
		dbConn:               dbConn,
		rabbitMqManager:      rabbitMqManager,
		logger:               logger,
		queries:              queries,
		terraformExecPath:    env["TERRAFORM_EXEC_PATH"],
		graphClient:          graphClient,
		standardUsersGroupID: env["STANDARD_USERS_GROUP_ID"],
		adminUsersGroupID:    env["ADMIN_USERS_GROUP_ID"],
		awsRootRegion:        env["AWS_ROOT_REGION"],
		awsRootEmail:         env["AWS_ROOT_EMAIL"],
	}

	return app, env, nil
}

// func PrivateNetworkMiddleware(ctx huma.Context, next func(huma.Context)) {
// 	ctx.SetHeader("Access-Control-Allow-Private-Network", "true")
// 	next(ctx)
// }

func PNAMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Access-Control-Allow-Private-Network", "true")
			next.ServeHTTP(w, r)
		})
	}
}

func main() {

	// Router
	router := chi.NewMux()

	// Application
	app, env, err := InitApplication()
	if err != nil {
		log.Fatal(err)
	}

	defer app.dbConn.Close()
	defer app.rabbitMqManager.Close()

	// PNA Middleware
	router.Use(PNAMiddleware())

	// Healthcheck
	router.Use(middleware.Heartbeat("/healthcheck"))

	// CORS
	router.Use(cors.Handler(cors.Options{
		AllowedOrigins:   []string{env["CORS_URL"]},
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token"},
		ExposedHeaders:   []string{"Link"},
		AllowCredentials: true,
		MaxAge:           300,
	}))

	// Config options
	config := huma.DefaultConfig("Engage API", "2.0.0")
	config.Components.SecuritySchemes = map[string]*huma.SecurityScheme{
		"microsoft_entra_auth": {
			Type: "oauth2",
			Flows: &huma.OAuthFlows{
				AuthorizationCode: &huma.OAuthFlow{
					AuthorizationURL: "https://example.com/oauth/authorize",
					TokenURL:         "https://example.com/oauth/token",
					Scopes: map[string]string{
						"scope1": "Scope 1 description...",
						"scope2": "Scope 2 description...",
					},
				},
			},
		},
	}
	api := humachi.New(router, config)

	// Commented out to use the Chi approach
	// api.UseMiddleware(PrivateNetworkMiddleware)

	// Authentication middleware
	api.UseMiddleware(NewAuthMiddleware(api, app, fmt.Sprintf("https://login.microsoftonline.com/%s/discovery/v2.0/keys", env["AZURE_TENANT_ID"]), env["AZURE_APP_ID"], env["AZURE_TENANT_ID"], env["STANDARD_USERS_GROUP_ID"], env["ADMIN_USERS_GROUP_ID"]))

	// Register routes
	addAdminRoutes(api, app)
	addEngagementRoutes(api, app)
	addNodeGroupsRoutes(api, app)
	addNodesRoutes(api, app)
	addInstancessRoutes(api, app)
	addNodeTypeCloudInstanceRoutes(api, app)
	addNodeTypeHostRoutes(api, app)
	addNodeTypeEmailAddressRoutes(api, app)
	addNodeTypeUrlRoutes(api, app)
	addNodeTypePersonRoutes(api, app)
	addProvidersRoutes(api, app)
	addUsersRoutes(api, app)
	addClientsRoutes(api, app)
	addInventoryRoutes(api, app)
	addDeploymentsRoutes(api, app)
	addUserAssignmentLogsRoutes(api, app)
	addAccountRoutes(api, app)
	addDomainsRoutes(api, app)

	engagements, err := app.queries.GetAllEngagements(context.Background())
	if err != nil {
		log.Fatalf("Failed to list engagements: %s", err.Error())
	}

	// Set QoS for the aws-account-create-q queue
	ch, err := app.getChannel()
	if err != nil {
		app.logger.Error("Failed to get RabbitMQ channel", "error", err.Error())
		panic("Failed to get RabbitMQ channel")
	}

	err = ch.Qos(1, 0, false) // Ensures only 1 message at a time per consumer
	if err != nil {
		app.logger.Error("Failed to set QoS for aws-account-create-q", "error", err.Error())
		panic("Failed to set QoS for aws-account-create-q")
	}
	_, err = ch.QueueDeclare(
		"aws-account-create-q", // queue name
		true,                   // durable
		false,                  // delete when unused
		false,                  // exclusive
		false,                  // no-wait
		nil,                    // arguments
	)
	if err != nil {
		app.logger.Error("Failed to declare aws-account-create-q", "error", err.Error())
		panic("Failed to declare aws-account-create-q")
	}
	app.logger.Info("Declared aws-account-create-q")

	msgs, err := ch.Consume(
		"aws-account-create-q", // queue name
		"",                     // consumer tag
		false,                  // auto-ack
		false,                  // exclusive
		false,                  // no-local
		false,                  // no-wait
		nil,                    // arguments
	)
	if err != nil {
		app.logger.Error("Failed to register consumer", "error", err.Error())
		panic("Failed to register consumer")
	}
	err = accounts.StartAWSAccountWorker(ch, app.logger, app.queries, msgs)
	if err != nil {
		app.logger.Error("Failed to start AWS account creation worker", "error", err.Error())
		panic("Failed to start AWS account creation worker")
	}

	err = os.Chdir("deployments_playground")
	if err != nil {
		log.Fatalf("Failed to reference deployments_playground folder: %s", err.Error())
	}

	//Initialise RabbitMQ consumers for each Engagement
	// Check if RabbitMQ connection is available
	if !app.rabbitMqManager.IsConnected() {
		app.logger.Error("RabbitMQ connection is not available, cannot declare queues for engagements")
		panic("RabbitMQ connection is not available")
	}

	for _, engagement := range engagements {
		ch, err := app.getChannel()
		if err != nil {
			app.logger.Error("Failed to get RabbitMQ channel for engagement",
				"engagement_title", engagement.Title,
				"error", err.Error())
			continue // Skip this engagement instead of failing completely
		}

		err = ch.Qos(1, 0, false)
		if err != nil {
			app.logger.Error("Failed to set QoS for engagement",
				"engagement_title", engagement.Title,
				"error", err.Error())
			continue // Skip this engagement instead of failing completely
		}

		engagementIDString, err := converters.PgTypeUUIDToString(engagement.ID)
		if err != nil {
			log.Fatalf("Failed to convert Engagement ID to string: %v", err.Error())
		}

		// Retry queue declaration with exponential backoff
		var q amqp.Queue
		maxRetries := 3
		for attempt := 1; attempt <= maxRetries; attempt++ {
			q, err = ch.QueueDeclare(
				*engagementIDString, // name
				true,                // durable
				false,               // delete when unused
				false,               // exclusive
				false,               // no-wait
				nil,                 // arguments
			)
			if err == nil {
				break // Success
			}

			app.logger.Warn("Failed to declare queue for Engagement, retrying",
				"queue_name", *engagementIDString,
				"engagement_id", *engagementIDString,
				"engagement_title", engagement.Title,
				"error", err.Error(),
				"attempt", attempt,
				"max_retries", maxRetries)

			if attempt < maxRetries {
				time.Sleep(time.Duration(attempt) * time.Second) // Exponential backoff
			}
		}

		if err != nil {
			app.logger.Error("Failed to declare queue for Engagement after retries",
				"queue_name", *engagementIDString,
				"engagement_id", *engagementIDString,
				"engagement_title", engagement.Title,
				"error", err.Error(),
				"rabbitmq_host", env["RABBITMQ_HOST"],
				"rabbitmq_port", env["RABBITMQ_PORT"])
			panic(fmt.Sprintf("Failed to declare queue for Engagement %s after %d retries: %v", engagement.Title, maxRetries, err))
		}
		app.logger.Info("Queue declared for Engagement", "queue_name", *engagementIDString, "engagement_id", *engagementIDString, "engagement_title", engagement.Title)

		msgs, err := ch.Consume(
			q.Name, // queue
			"",     // consumer
			false,  // auto-ack
			false,  // exclusive
			false,  // no-local
			false,  // no-wait
			nil,    // args
		)
		if err != nil {
			app.logger.Error("Failed to register consumer for Engagement", "engagement_id", engagement.ID, "engagement_title", engagement.Title)
			panic("Failed to register consumer for Engagement")
		}

		app.logger.Info("Consumer registered for Engagement", "queue_name", *engagementIDString, "engagement_id", *engagementIDString, "engagement_title", engagement.Title)

		go func() {
			for d := range msgs {
				app.logger.Info("Received message for Engagement",
					"engagement_id", engagement.ID,
					"engagement_title", engagement.Title,
					"message_body", string(d.Body),
				)

				err := deployments.Worker(
					app.awsRootRegion,
					app.secretKey,
					app.queries,
					app.logger,
					app.terraformExecPath,
					d.Body,
				)

				if err != nil {
					app.logger.Error("Deployment worker failed",
						"engagement_id", engagement.ID,
						"engagement_title", engagement.Title,
						"error", err.Error(),
					)
				} else {
					// Use the connection manager's PublishWithRetry method
					syncTask := map[string]string{"task": "sync_cloud_instances"}
					body, marshalErr := json.Marshal(syncTask)
					if marshalErr != nil {
						app.logger.Error("Failed to marshal sync task", "error", marshalErr)
					} else if publishErr := app.rabbitMqManager.PublishWithRetry(
						context.Background(),
						"",                          // exchange
						"cloud-instance-sync-queue", // routing key
						false, false,                // mandatory, immediate
						amqp.Publishing{
							ContentType: "application/json",
							Body:        body,
						},
					); publishErr != nil {
						app.logger.Error("Failed to publish sync task after deployment", "error", publishErr)
					} else {
						app.logger.Info("Published cloud sync task after deployment")
					}
				}

				// ACK the message to avoid retrying for failed messages
				if ackErr := d.Ack(false); ackErr != nil {
					app.logger.Error("Failed to ACK message",
						"engagement_id", engagement.ID,
						"engagement_title", engagement.Title,
						"error", ackErr.Error(),
					)
				}
			}
		}()
	}

	type CloudProvider struct {
		Name      string
		QueueName string
		SyncFunc  func(ctx context.Context, queries *db.Queries, logger *slog.Logger) error
	}

	providers := []CloudProvider{
		{
			Name:      "aws",
			QueueName: "aws-sync-queue",
			SyncFunc: func(ctx context.Context, queries *db.Queries, logger *slog.Logger) error {
				awsScheduler.SyncAWSAccountStatus(ctx, queries, logger)
				return nil
			},
		},
		{
			Name:      "azure",
			QueueName: "azure-sync-queue",
			SyncFunc: func(ctx context.Context, queries *db.Queries, logger *slog.Logger) error {
				return azureScheduler.SyncAzureTenantSubscriptionStatus(ctx, queries, logger)
			},
		},
	}

	for _, provider := range providers {
		p := provider
		ch, err := app.getChannel()
		if err != nil {
			app.logger.Error("Failed to get RabbitMQ channel for provider", "provider", p.Name, "error", err.Error())
			continue
		}
		scheduler.StartAccountSyncWorker(
			ch,
			p.QueueName,
			app.queries,
			app.logger.With("provider", p.Name),
			p.SyncFunc,
		)
	}

	for _, provider := range providers {
		p := provider
		go func() {
			ticker := time.NewTicker(720 * time.Minute)
			defer ticker.Stop()
			app.logger.Info(fmt.Sprintf("Started %s account sync ticker", p.Name))
			for range ticker.C {
				ch, err := app.getChannel()
				if err != nil {
					app.logger.Error(fmt.Sprintf("Failed to get RabbitMQ channel for %s sync task", p.Name), "error", err)
					continue
				}
				err = scheduler.PublishAccountSyncTask(context.Background(), ch, p.QueueName)
				if err != nil {
					app.logger.Error(fmt.Sprintf("Failed to publish %s sync task", p.Name), "error", err)
				} else {
					app.logger.Info(fmt.Sprintf("Published %s sync task", p.Name))
				}
			}
		}()
	}

	// Start AWS cloud instance sync worker
	awsCh, err := app.getChannel()
	if err != nil {
		app.logger.Error("Failed to get RabbitMQ channel for AWS cloud instance sync worker", "error", err.Error())
	} else {
		awsScheduler.StartCloudInstanceSyncWorker(awsCh, "cloud-instance-sync-queue", app.queries, app.logger)
	}

	go func() {
		ticker := time.NewTicker(60 * time.Minute)
		defer ticker.Stop()
		app.logger.Info("Started AWS cloud instance sync ticker")
		for {
			<-ticker.C
			ch, err := app.getChannel()
			if err != nil {
				app.logger.Error("Failed to get RabbitMQ channel for AWS cloud instance sync task", "error", err)
				continue
			}
			err = awsScheduler.PublishCloudInstanceSyncTask(context.Background(), ch, "cloud-instance-sync-queue")
			if err != nil {
				app.logger.Error("Failed to publish AWS cloud instance sync task", "error", err)
			} else {
				app.logger.Info("Published AWS cloud instance sync task")
			}
		}
	}()

	// Start Azure cloud instance sync worker
	azureCh, err := app.getChannel()
	if err != nil {
		app.logger.Error("Failed to get RabbitMQ channel for Azure cloud instance sync worker", "error", err.Error())
	} else {
		azureScheduler.StartAzureCloudInstanceSyncWorker(azureCh, "azure-cloud-instance-sync-queue", app.queries, app.logger)
	}

	go func() {
		ticker := time.NewTicker(60 * time.Minute)
		defer ticker.Stop()
		app.logger.Info("Started Azure cloud instance sync ticker")
		for {
			<-ticker.C
			ch, err := app.getChannel()
			if err != nil {
				app.logger.Error("Failed to get RabbitMQ channel for Azure cloud instance sync task", "error", err)
				continue
			}
			err = azureScheduler.PublishAzureCloudInstanceSyncTask(context.Background(), ch, "azure-cloud-instance-sync-queue")
			if err != nil {
				app.logger.Error("Failed to publish Azure cloud instance sync task", "error", err)
			} else {
				app.logger.Info("Published Azure cloud instance sync task")
			}
		}
	}()

	port := 8080
	app.logger.Info("Running server", "port", port)
	err = http.ListenAndServe(fmt.Sprintf("0.0.0.0:%d", port), router)
	if err != nil {
		log.Fatalf("Failed to start server: %s", err.Error())
	}
}
