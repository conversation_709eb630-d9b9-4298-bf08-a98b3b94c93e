package main

import (
	"context"
	"fmt"
	"net/http"
	"net/netip"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/activitylogs"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/aws"
)

func addNodeTypeCloudInstanceRoutes(api huma.API, a *application) {
	// Get Node Type Cloud Instance
	type GetNodeCloudInstanceInput struct {
		NodeID              string `path:"nodeID" format:"uuid" doc:"Node ID" example:"a58a3afc-c34e-440d-ba75-2045bb0c7577"`
		IncludeActivityLogs bool   `query:"activity_logs" doc:"Whether to include activity logs (optional). Example: true or false." default:"true"`
	}

	type NodeCloudInstance struct {
		Provider               string      `json:"provider" enum:"AWS,GCP,AZURE"`
		Region                 string      `json:"region" example:"eu-west-2"`
		OperatingSystemImageID string      `json:"operating_system_image_id"`
		InstanceType           string      `json:"instance_type" doc:"AWS Instance type" example:"t2.micro"`
		Name                   string      `json:"name" doc:"Instance name" pattern:"^[a-zA-Z0-9 _.:/=+\\-@]{1,256}$" minLength:"1" maxLength:"256"`
		PublicIpV4Address      *netip.Addr `json:"public_ipv4_address" format:"ipv4"`
		OpenIngressTcpPorts    []int32     `json:"open_ingress_tcp_ports" doc:"Open ingress TCP ports" example:"[22]"`
		NodeID                 string      `json:"node_id" format:"uuid" doc:"Node ID"`
		CideploymentStatus     string      `json:"ci_deployment_status" enum:"PENDING,IN-PROGRESS,SUCCESS,WARNING,ERROR"`
		CloudInstanceState     string      `json:"cloud_instance_state" enum:"pending,running,stopping,stopped,shutting-down,terminated,error,new"`
		CloudInstanceID        string      `json:"cloud_instance_id" example:"i-034295fe21c3bebf7"`
	}

	type GetNodeCloudInstanceOutput struct {
		Body struct {
			Node         NodeCloudInstance `json:"node"`
			ActivityLogs []ActivityLog     `json:"activity_logs,omitempty" doc:"Activity Logs"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-nodes-cloud-instance",
		Method:        http.MethodGet,
		Path:          "/nodes/cloud_instance/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get a Cloud Instance Node",
		Tags:          []string{"Nodes, Cloud Instance"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *GetNodeCloudInstanceInput) (*GetNodeCloudInstanceOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetNodeCloudInstanceOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, err
		}
		node, err := a.queries.GetNodeTypeCloudInstance(context.Background(), db.GetNodeTypeCloudInstanceParams{
			NodeID: *nodeIDPgType,
			ID:     *userIDPgType,
		})
		if err != nil {
			a.logger.Error("Error getting Node Cloud Instance", "nodeID", i.NodeID, "error", err)
			return nil, huma.Error404NotFound("Node not found")
		}

		resp.Body.Node.Provider = string(node.Provider)
		resp.Body.Node.Region = node.Region
		resp.Body.Node.OperatingSystemImageID = node.OperatingSystemImageID
		resp.Body.Node.InstanceType = node.InstanceSize
		resp.Body.Node.Name = node.InstanceName
		resp.Body.Node.PublicIpV4Address = node.PublicIpv4Address
		resp.Body.Node.OpenIngressTcpPorts = node.OpenPorts
		resp.Body.Node.NodeID = i.NodeID
		resp.Body.Node.CideploymentStatus = string(node.CiDeploymentStatus)
		resp.Body.Node.CloudInstanceState = string(node.CloudInstanceState.CiStateEnum)
		resp.Body.Node.CloudInstanceID = node.CloudInstanceID.String

		if i.IncludeActivityLogs {
			activityLogs := make([]ActivityLog, 0)
			activityLogsDB, err := a.queries.GetNodeActivityLogs(context.Background(), *nodeIDPgType)
			if err != nil {
				a.logger.Error("Error getting activity logs", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
				return nil, huma.Error404NotFound("Activity logs not found")
			}
			for _, activityLogDB := range activityLogsDB {
				activityLogs = append(activityLogs, ActivityLog{
					Message:   activityLogDB.Message,
					Type:      activityLogDB.Type,
					Username:  activityLogDB.Username,
					CreatedAt: activityLogDB.CreatedAt.Time,
				})
			}
			resp.Body.ActivityLogs = activityLogs
		}
		return resp, nil
	})

	// Edit Node Type Cloud Instance
	type EditNodeTypeCloudInstanceInput struct {
		NodeID string `path:"nodeID" format:"uuid" doc:"Node ID" example:"a58a3afc-c34e-440d-ba75-2045bb0c7577"`
		Body   struct {
			Name                string  `json:"name"`
			OpenIngressTcpPorts []int32 `json:"open_ingress_tcp_ports"`
		}
	}

	type EditNodeTypeCloudInstanceOutput struct {
		Body struct {
			Provider               string      `json:"provider" enum:"AWS,GCP,AZURE"`
			Region                 string      `json:"region" example:"eu-west-2"`
			OperatingSystemImageID string      `json:"operating_system_image_id"`
			InstanceType           string      `json:"instance_type" doc:"AWS Instance type" example:"t2.micro"`
			Name                   string      `json:"name" doc:"Instance name" pattern:"^[a-zA-Z0-9 _.:/=+\\-@]{1,256}$" minLength:"1" maxLength:"256"`
			PublicIpV4Address      *netip.Addr `json:"public_ipv4_address" format:"ipv4"`
			OpenIngressTcpPorts    []int32     `json:"open_ingress_tcp_ports" doc:"Open ingress TCP ports" example:"[22]"`
			NodeID                 string      `json:"node_id" format:"uuid"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-nodes-cloud-instance",
		Method:        http.MethodPut,
		Path:          "/nodes/cloud_instance/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Edit a Cloud Instance Node",
		Tags:          []string{"Nodes, Cloud Instance"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *EditNodeTypeCloudInstanceInput) (*EditNodeTypeCloudInstanceOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &EditNodeTypeCloudInstanceOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		updatedNodeCloudInstance, err := a.queries.UpdateNodeTypeCloudInstance(context.Background(),
			db.UpdateNodeTypeCloudInstanceParams{
				NodeID:    *nodeIDPgType,
				Name:      i.Body.Name,
				OpenPorts: i.Body.OpenIngressTcpPorts,
			})
		if err != nil {
			a.logger.Error("Error updating CloudInstance Node in database", "nodeID", i.NodeID, "error", err)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// Update the nodes table with the name
		err = a.queries.UpdateNodeName(ctx, db.UpdateNodeNameParams{
			Name: i.Body.Name,
			ID:   *nodeIDPgType,
		})
		if err != nil {
			a.logger.Error("Error updating Node name in database", "nodeID", i.NodeID, "error", err)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Updated successfully", db.LogsNodesTypeEnumNODEUPDATE, *userIDPgType, *nodeIDPgType, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// TODO potential code for editing an instance with Terraform?
		//node, err := a.queries.GetNodeTypeCloudInstance(context.Background(), db.GetNodeTypeCloudInstanceParams{
		//	NodeID: *nodeIDPgType,
		//	ID:     *userIDPgType,
		//})
		//if err != nil {
		//	return nil, huma.Error500InternalServerError("Something went wrong")
		//}
		//
		//awsInstanceDeployment, _ := deployments.NewAwsCloudInstanceDeployment(
		//	a.queries,
		//	a.rabbitMqChannel,
		//	*userIDPgType,
		//	*nodeIDPgType,
		//	node.EngagementID,
		//	updatedNodeCloudInstance.Region,
		//	updatedNodeCloudInstance.OperatingSystemImageID,
		//	updatedNodeCloudInstance.Size,
		//	updatedNodeCloudInstance.Name,
		//	updatedNodeCloudInstance.OpenPorts)
		//
		//err = awsInstanceDeployment.WriteTemplate()
		//if err != nil {
		//	a.logger.Error("Error generating Terraform template", "error", err.Error())
		//	return nil, huma.Error500InternalServerError("Something went wrong")
		//}

		a.logger.Info("Updated CloudInstance Node successfully", "node_id", i.NodeID)
		resp.Body.NodeID = i.NodeID
		resp.Body.Name = updatedNodeCloudInstance.Name
		resp.Body.Region = updatedNodeCloudInstance.Region
		resp.Body.Provider = string(updatedNodeCloudInstance.Provider)
		resp.Body.OpenIngressTcpPorts = updatedNodeCloudInstance.OpenPorts
		resp.Body.OperatingSystemImageID = updatedNodeCloudInstance.OperatingSystemImageID
		resp.Body.InstanceType = updatedNodeCloudInstance.InstanceType

		return resp, nil
	})

	// Create Node Cloud Instance
	// AWS tag requirements: https://docs.aws.amazon.com/pdfs/tag-editor/latest/userguide/tag-editor-userguide.pdf

	type OperatingSystemImageAzure struct {
		Publisher string `json:"publisher,omitempty"`
		Offer     string `json:"offer,omitempty"`
		SKU       string `json:"sku,omitempty"`
		Version   string `json:"version,omitempty"`
	}
	type NodeCloudInstanceInput struct {
		Body struct {
			EngagementID           string                     `json:"engagement_id" doc:"Engagement ID" format:"uuid" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
			NodeGroupID            string                     `json:"node_group_id,omitempty" format:"uuid" doc:"Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created" example:"9a7e965f-43ed-434b-bf08-059e8dca0111"`
			Provider               string                     `json:"provider" enum:"AWS,GCP,AZURE"`
			Region                 string                     `json:"region" example:"eu-west-2"`
			OperatingSystemImageID string                     `json:"operating_system_image_id,omitempty"` // for AWS
			OperatingSystemImage   *OperatingSystemImageAzure `json:"operating_system_image,omitempty"`    // for Azure
			InstanceType           string                     `json:"instance_type" doc:"AWS Instance type" example:"t2.micro"`
			Name                   string                     `json:"name" doc:"Instance name" pattern:"^[a-zA-Z0-9 _.:/=+\\-@]{1,256}$" minLength:"1" maxLength:"256"`
			OpenIngressTcpPorts    []int32                    `json:"open_ingress_tcp_ports" doc:"Open ingress TCP ports" example:"[22]"`
			StartupScript          string                     `json:"startup_script,omitempty" doc:"Startup script in base64 encoding" example:"c3VkbyBhcHQgdXBkYXRlIC15ICYmIHN1ZG8gYXB0IGluc3RhbGwgLXkgcHl0aG9uMyBweXRob24zLXBpcA=="`
			SelectedAccountID      string                     `json:"selected_account_id"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-cloud-instance",
		Method:        http.MethodPost,
		Path:          "/nodes/cloudinstance",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create a Cloud Instance Node for an Engagement",
		Tags:          []string{"Nodes, Cloud Instance"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, i *NodeCloudInstanceInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		engagementIDPgType, err := converters.StringToPgTypeUUID(i.Body.EngagementID)
		if err != nil {
			a.logger.Error("Error parsing EngagementID", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		var associatedNodeGroupID pgtype.UUID

		// No Node Group ID was provided to create the Node in, create a new one
		if len(i.Body.NodeGroupID) == 0 {
			associatedNodeGroup, err := a.queries.CreateNodeGroup(context.Background(), db.CreateNodeGroupParams{
				Name:         "New Node Group",
				IsActive:     true,
				EngagementID: *engagementIDPgType,
				CreatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
				UpdatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
			})
			if err != nil {
				a.logger.Error("Error creating new Node Group in database", "error", err.Error(),
					"user_id", userID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "cloud_instance",
					"node_group", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			associatedNodeGroupID = associatedNodeGroup.ID
		} else {
			// An existing Node Group ID was provided, check if it is associated with the Engagement
			// and associate it with the new Node
			nodeGroupIDPgType, err := converters.StringToPgTypeUUID(i.Body.NodeGroupID)
			if err != nil {
				a.logger.Error("Error parsing NodeGroupID", "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			nodeGroupsDB, err := a.queries.GetEngagementNodeGroup(context.Background(), db.GetEngagementNodeGroupParams{
				ID:           *nodeGroupIDPgType,
				EngagementID: *engagementIDPgType,
			})
			if err != nil {
				a.logger.Error("Error getting Node Groups", "error", err.Error(), "node_group_id", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			// If the Node Group is associated with the Engagement, use its ID
			// Otherwise return a generic error to the user for security reasons
			if len(nodeGroupsDB) == 1 {
				associatedNodeGroupID = nodeGroupsDB[0].ID
			} else {
				a.logger.Error("Error getting Node Group associated with Engagement while creating a Node",
					"user_id", userID,
					"node_group_id", i.Body.NodeGroupID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "cloud_instance")
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}

		createdNode, err := a.queries.CreateNode(context.Background(), db.CreateNodeParams{
			NodeType:    "CLOUD_INSTANCE",
			Name:        i.Body.Name,
			NodeGroupID: associatedNodeGroupID,
		})
		if err != nil {
			a.logger.Error("Error creating Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "cloud_instance",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		params := db.CreateCloudInstanceNodeParams{
			Provider:           db.ProviderEnum(i.Body.Provider),
			Region:             i.Body.Region,
			InstanceType:       i.Body.InstanceType,
			Name:               i.Body.Name,
			NodeID:             createdNode.ID,
			OpenPorts:          i.Body.OpenIngressTcpPorts,
			CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnumNew, Valid: true},
		}

		// cloud provider dependent params adding
		accountUUID, err := converters.StringToPgTypeUUID(i.Body.SelectedAccountID)
		if err != nil {
			a.logger.Error("Invalid Cloud Account ID", "err", err)
			return nil, huma.Error400BadRequest("Invalid Cloud Account ID")
		}

		switch i.Body.Provider {
		case "AWS":
			params.AwsAccountID = *accountUUID
			params.OperatingSystemImageID = i.Body.OperatingSystemImageID

		case "AZURE":
			params.AzureTenantID = *accountUUID
			img := i.Body.OperatingSystemImage
			params.OperatingSystemImageID = fmt.Sprintf("%s:%s:%s:%s", img.Publisher, img.Offer, img.SKU, img.Version)

		default:
			return nil, huma.Error400BadRequest("Unsupported provider")
		}

		err = a.queries.CreateCloudInstanceNode(context.Background(), params)

		if err != nil {
			a.logger.Error("Error creating Cloud Instance Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "cloud_instance",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Created successfully", db.LogsNodesTypeEnumNODECREATION, *userIDPgType, createdNode.ID, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "user_id", userID, "error", err)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		switch i.Body.Provider {
		case "AWS":
			sshPublicKey, err := a.queries.GetAccountSshPublicKey(ctx, *accountUUID)

			if err != nil {
				a.logger.Error("Failed to retrieve public SSH key for Account", "user_id", userID, "error", err)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			ch, err := a.getChannel()
			if err != nil {
				return nil, huma.Error400BadRequest("Failed to get RabbitMQ channel", err)
			}

			awsCloudInstanceDeployment, _ := deployments.NewAwsCloudInstanceDeployment(
				a.queries,
				ch,
				*userIDPgType,
				createdNode.ID,
				*engagementIDPgType,
				i.Body.Region,
				i.Body.OperatingSystemImageID,
				i.Body.InstanceType,
				i.Body.Name,
				i.Body.OpenIngressTcpPorts,
				i.Body.StartupScript,
				sshPublicKey,
				a.awsRootRegion,
				i.Body.SelectedAccountID)

			err = awsCloudInstanceDeployment.WriteTemplate()
			if err != nil {
				a.logger.Error("Error generating Terraform template", "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

		case "AZURE":
			sshPublicKey, err := a.queries.GetAzureSshPublicKey(ctx, *accountUUID)

			if err != nil {
				a.logger.Error("Failed to retrieve public SSH key for Account", "user_id", userID, "error", err)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			ch, err := a.getChannel()
			if err != nil {
				return nil, huma.Error400BadRequest("Failed to get RabbitMQ channel", err)
			}

			azureCloudInstanceDeployment, _ := deployments.NewAzureCloudInstanceDeployment(
				a.queries,
				ch,
				*userIDPgType,
				createdNode.ID,
				*engagementIDPgType,
				i.Body.Region,
				i.Body.OperatingSystemImage.Publisher,
				i.Body.OperatingSystemImage.Offer,
				i.Body.OperatingSystemImage.SKU,
				i.Body.OperatingSystemImage.Version,
				i.Body.InstanceType,
				i.Body.Name,
				i.Body.OpenIngressTcpPorts,
				i.Body.StartupScript,
				a.awsRootRegion,
				sshPublicKey,
				i.Body.SelectedAccountID,
			)
			err = azureCloudInstanceDeployment.WriteTemplate()
			if err != nil {
				a.logger.Error("Error generating Terraform template", "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		default:
			a.logger.Error("Unsupported provider", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		createdNodeIDString, err := converters.PgTypeUUIDToString(createdNode.ID)
		if err != nil {
			a.logger.Error("Error converting Node ID to string", "error", err.Error(), "node_type", createdNode.NodeType, "node_id", createdNode.ID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		a.logger.Info("Created Cloud Instance Node successfully",
			"user_id", userID,
			"engagement_id", i.Body.EngagementID,
			"provider", params.Provider,
			"region", i.Body.Region,
			"operating_system_image_id", params.OperatingSystemImageID,
			"size", i.Body.InstanceType,
			"name", i.Body.Name,
			"open_ingress_tcp_ports", i.Body.OpenIngressTcpPorts,
			"startup_script", i.Body.StartupScript,
			"node_id", createdNodeIDString,
			"selected_account_ID", i.Body.SelectedAccountID)
		return nil, nil
	})

	// Get instance types with priorities first from the instance_size_mappings table and check availability in the region
	type GetInstanceTypesDBOutput struct {
		Body struct {
			InstanceTypes []struct {
				Alias string `json:"alias"`
				Type  string `json:"type"`
			} `json:"instance_types"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-instance-types",
		Method:        http.MethodGet,
		Path:          "/nodes/cloud_instance/instance-types/{region}/{amiId}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get instance types from the database",
		Tags:          []string{"Nodes, Cloud Instance, Instances"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *struct {
		Region string `path:"region" maxLength:"20" example:"eu-west-2" doc:"AWS Region"`
		AmiId  string `path:"amiId" example:"ami-0123456789abcdef0" doc:"AMI ID to filter compatible instance types"`
	}) (*GetInstanceTypesDBOutput, error) {

		// Create AWS EC2 client
		awsEc2Client := aws.NewEc2Client(i.Region)

		// Get instance types
		instanceTypes, err := awsEc2Client.GetInstanceTypes(a.queries, i.AmiId)
		if err != nil {
			a.logger.Error("Error getting instance types", "error", err)
			return nil, huma.Error500InternalServerError("Failed to get instance types")
		}

		// If no instance types are found in the database, fallback to AWS API
		if len(instanceTypes) == 0 {
			a.logger.Warn("No instance types found in database, falling back to AWS API", "region", i.Region, "amiId", i.AmiId)

			instanceTypes, err = awsEc2Client.GetInstanceTypes(a.queries, i.AmiId)
			if err != nil {
				a.logger.Error("Error getting instance types from AWS API", "error", err)
				return nil, huma.Error500InternalServerError("Failed to get instance types from AWS API")
			}
		}

		// Prepare response
		resp := &GetInstanceTypesDBOutput{}
		resp.Body.InstanceTypes = make([]struct {
			Alias string `json:"alias"`
			Type  string `json:"type"`
		}, len(instanceTypes))

		for i, instanceType := range instanceTypes {
			resp.Body.InstanceTypes[i] = struct {
				Alias string `json:"alias"`
				Type  string `json:"type"`
			}{
				Alias: instanceType.Alias,
				Type:  instanceType.Type,
			}
		}

		return resp, nil
	})
}
