package main

import (
	"context"
	"fmt"
	"net/http"
	"time"

	huma "github.com/danielgtaylor/huma/v2"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/accounts"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
)

func addAccountRoutes(api huma.API, a *application) {

	type CreateAWSAccountInput struct {
		EngagementID string `path:"engagementId" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
		Body         struct {
			NickName string `json:"nickname" doc:"AWS Account Nickname"`
		}
	}

	// Create AWS Account
	huma.Register(api, huma.Operation{
		OperationID:   "post-create-aws-account",
		Method:        http.MethodPost,
		Path:          "/engagements/{engagementId}/addAWSAccount",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Request creation of AWS account for an engagement",
		Tags:          []string{"AWSAccounts, Engagements"},
		DefaultStatus: http.StatusAccepted,
	}, func(ctx context.Context, i *CreateAWSAccountInput) (*struct{}, error) {
		createdBy, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("User ID missing from context")
		}
		engagementID := i.EngagementID
		if engagementID == "" {
			return nil, huma.Error400BadRequest("engagementID is required but not provided in the path")
		}
		engagementUUID, err := converters.StringToPgTypeUUID(engagementID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to convert engagementID")
		}

		awsAccounts, err := a.queries.ListAWSAccountsByEngagementID(context.Background(), *engagementUUID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to list AWS accounts for nickname check")
		}

		// Iterate through the AWS accounts and check if the user entered nickname already exists
		for _, account := range awsAccounts {
			if account.Nickname == i.Body.NickName {
				return nil, &huma.ErrorModel{
					Status: 400,
					Title:  "Bad Request",
					Detail: fmt.Sprintf("Nickname '%s' already exists for this engagement. Retry using other nickname.", i.Body.NickName),
				}
			}
		}
		ch, err := a.getChannel()
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to get RabbitMQ channel", err)
		}

		err = accounts.PublishAWSAccountCreationMessage(
			ch,
			engagementID,
			a.secretKey,
			a.awsRootEmail,
			a.awsRootRegion,
			a.logger,
			a.queries,
			i.Body.NickName,
			createdBy,
		)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to publish msg for AWS account creation")
		}

		return nil, nil
	})

	type CreateAzureTenantInput struct {
		EngagementID string                   `path:"engagementId"`
		Body         accounts.AzureTenantForm `json:"body"`
	}

	type ValidationErrorResponse struct {
		Error []accounts.TenantError `json:"error"`
	}

	// add user input for Azure tenants
	huma.Register(api, huma.Operation{
		OperationID:   "post-create-azure-tenant",
		Method:        http.MethodPost,
		Path:          "/engagements/{engagementId}/addAzureTenant",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Add an Azure tenant to an engagement",
		Tags:          []string{"AzureTenants", "Engagements"},
		DefaultStatus: http.StatusAccepted,
	}, func(ctx context.Context, i *CreateAzureTenantInput) (*ValidationErrorResponse, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok || userID == "" {
			return nil, huma.Error500InternalServerError("User ID missing from context")
		}

		if i.EngagementID == "" {
			return nil, huma.Error400BadRequest("engagementID is required")
		}

		errorMessages, err := accounts.AddAzureTenantData(
			a.logger,
			a.dbConn,
			a.queries,
			i.EngagementID,
			a.secretKey,
			a.awsRootEmail,
			a.awsRootRegion,
			i.Body,
		)

		if len(errorMessages) > 0 {
			fmt.Println("Validation errors occurred for tenants:", "err", errorMessages)
			return nil, huma.Error400BadRequest(fmt.Sprintf("Validation failed: %s", errorMessages))
		}

		if err != nil {
			return nil, huma.Error500InternalServerError(fmt.Sprintf("Failed to add Azure Tenant Data: %s", err.Error()))
		}

		return nil, nil

	})

	type AWSAccount struct {
		AccountName           string    `json:"account_name"`
		AccountCreationStatus string    `json:"account_creation_status"`
		CreatedAt             time.Time `json:"created_at"`
		AccountCloudID        string    `json:"account_cloud_id"`
		AccountCloudStatus    string    `json:"account_cloud_status"`
		NickName              string    `json:"nickname"`
		CreatedBy             string    `json:"created_by"`
	}

	type GetAWSAccountsOutput struct {
		Body struct {
			Accounts []AWSAccount `json:"accounts"`
		}
	}
	// get existing aws accounts
	huma.Register(api, huma.Operation{
		OperationID:   "get-engagement-aws-accounts",
		Method:        http.MethodGet,
		Path:          "/engagements/{engagementID}/aws-accounts",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get AWS accounts for an engagement",
		Tags:          []string{"Engagements", "AWS Accounts"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		EngagementID string `path:"engagementID" format:"uuid"`
	}) (*GetAWSAccountsOutput, error) {
		engagementID, err := converters.StringToPgTypeUUID(input.EngagementID)
		if err != nil {
			return nil, huma.Error400BadRequest("Invalid engagement ID")
		}

		awsAccounts, err := a.queries.ListAWSAccountsByEngagementID(ctx, *engagementID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to fetch AWS accounts")
		}

		resp := &GetAWSAccountsOutput{}
		for _, acc := range awsAccounts {
			resp.Body.Accounts = append(resp.Body.Accounts, AWSAccount{
				AccountName:           acc.ID.String(),
				AccountCreationStatus: acc.AccountCreationStatus.String,
				CreatedAt:             acc.CreatedAt.Time,
				AccountCloudID:        acc.CloudAccountID.String,
				AccountCloudStatus:    acc.AccountCloudStatus.String,
				NickName:              acc.Nickname,
				CreatedBy:             acc.CreatedBy,
			})
		}

		return resp, nil
	})

	type AzureTenant struct {
		TenantID           string    `json:"tenant_id"`
		SubscriptionID     string    `json:"subscription_id"`
		AccountCloudStatus string    `json:"account_cloud_status"`
		CreatedAt          time.Time `json:"created_at"`
		SecretsSaved       bool      `json:"secrets_saved"`
		CreationStatus     string    `json:"creation_status"`
	}

	type GetAzureTenantsOutput struct {
		Body struct {
			Tenants []AzureTenant `json:"tenants"`
		}
	}

	// get existing azure tenants
	huma.Register(api, huma.Operation{
		OperationID:   "get-engagement-azure-tenants",
		Method:        http.MethodGet,
		Path:          "/engagements/{engagementID}/azure-tenants",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Azure tenants for an engagement",
		Tags:          []string{"Engagements", "Tenants"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct {
		EngagementID string `path:"engagementID" format:"uuid"`
	}) (*GetAzureTenantsOutput, error) {

		engagementID, err := converters.StringToPgTypeUUID(input.EngagementID)
		if err != nil {
			return nil, huma.Error400BadRequest("Invalid engagement ID")
		}
		azureTenants, err := a.queries.ListAzureTenantsByEngagementID(ctx, *engagementID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to fetch Azure tenants")
		}

		resp := &GetAzureTenantsOutput{}
		for _, t := range azureTenants {
			resp.Body.Tenants = append(resp.Body.Tenants, AzureTenant{
				TenantID:           t.TenantID,
				SubscriptionID:     t.SubscriptionID.String,
				CreationStatus:     t.CreationStatus.String,
				CreatedAt:          t.CreatedAt.Time,
				SecretsSaved:       t.SecretsSaved,
				AccountCloudStatus: t.AccountCloudStatus.String,
			})
		}
		return resp, nil
	})
}
