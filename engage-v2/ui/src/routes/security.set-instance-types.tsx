import { createFileRoute } from "@tanstack/react-router";
import { useState, useEffect, useMemo, useCallback } from "react";
import { 
  Combobox, 
  ComboboxButton, 
  ComboboxInput, 
  ComboboxOption, 
  ComboboxOptions, 
  Tab,
  TabGroup,
  TabList
  
} from "@headlessui/react";
import { HiXMark, HiChevronDown, HiArrowUp } from "react-icons/hi2";
import { FaAws } from "react-icons/fa";
import { SiGooglecloud } from "react-icons/si";
import { VscAzure } from "react-icons/vsc";
import { ImSpinner8 } from "react-icons/im";

import { sanitizeInstanceType } from "../utils/security";
import { CloudProvider, cloudProviders } from "../components/CreateNode/CloudInstance/types";
import ResponsiveSideNav from "../components/ResponsiveSideNav";
import Header from "../components/Header";
import BreadCrumbs from "../components/BreadCrumbs";
import { QueryClient, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSetInstanceType, useGetProvidersAwsRegions, getGetAwsInstanceTypesQueryKey, getGetProvidersAwsRegionsQueryKey, getAwsInstanceTypes } from "../client";
import { InstanceSizeMapping } from "../model";
import { toast } from "react-toastify";
import { CustomRegions, CustomType } from "../types";
import { useCustomInstanceTypes } from "../hooks/useCustomInstanceTypes";

type CategoryType = "small" | "medium" | "large" | "custom_alpha" | "custom_beta" | "custom_gamma" | "custom_delta";
type OptionType = "primary" | "secondary" | "fallback";

type InstanceTypeSelections = {
  [K in CategoryType]: {
    [P in OptionType]: string | null;
  };
};

type MappingChange = {
  provider: string;
  size_alias: string;
  priority: number;
  instance_type: string;
  action: 'add' | 'delete';
};

const getProviderTabIcon = (provider: string) => {
  switch (provider) {
    case "AWS":
      return <FaAws className="h-5 w-5 text-[#FF9900]" />;
    case "GCP":
      return <SiGooglecloud className="h-5 w-5 text-[#4285F4]" />;
    case "AZURE":
      return <VscAzure className="h-5 w-5 text-[#008AD7]" />;
    default:
      return null;
  }
};

export const Route = createFileRoute("/security/set-instance-types")({
  component: InstanceTypeSettings,
  loader: ({ context: { queryClient } }: { context: { queryClient: QueryClient } }) => {
    const defaultRegion = "us-west-2";
    // Pre-fetch general instance types
    queryClient.prefetchQuery({
      queryKey: getGetAwsInstanceTypesQueryKey(defaultRegion),
      queryFn: () => getAwsInstanceTypes(defaultRegion),
    });

    // Pre-fetch all custom instance types
    const customTypes = ['custom_alpha', 'custom_beta', 'custom_gamma', 'custom_delta'] as const;
    customTypes.forEach(type => {
      queryClient.prefetchQuery({
        queryKey: getGetAwsInstanceTypesQueryKey(defaultRegion, {type}),
        queryFn: () => getAwsInstanceTypes(defaultRegion, {type}),
      });
    });
  },
});

export default function InstanceTypeSettings() {
  const queryClient = useQueryClient();
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<CloudProvider>(cloudProviders[0]);
  const [selectedRegion, setSelectedRegion] = useState<string>(() => {
    const savedRegion = localStorage.getItem('selectedInstanceTypeRegion');
    return savedRegion || "eu-west-2"; // Default region if none saved
  });
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const [validationErrors, setValidationErrors] = useState<{[key: string]: boolean}>({});
  const [, setHasChanges] = useState(false);
  const [typeQueries, setTypeQueries] = useState<{ [key: string]: string }>({});
  
  // Add custom regions state
  const [customRegions, setCustomRegions] = useState<CustomRegions>(() => ({
    custom_alpha: localStorage.getItem('customAlphaRegion') || '',
    custom_beta: localStorage.getItem('customBetaRegion') || '',
    custom_gamma: localStorage.getItem('customGammaRegion') || '',
    custom_delta: localStorage.getItem('customDeltaRegion') || ''
  }));

  // Add custom region change handler
  const handleCustomRegionChange = (category: keyof CustomRegions, region: string) => {
    // Get the previous region for this category
    const previousRegion = customRegions[category];
    
    // Update the custom regions state
    setCustomRegions(prev => ({
      ...prev,
      [category]: region
    }));
    
    // Store in localStorage
    const storageKeys = {
      custom_alpha: 'customAlphaRegion',
      custom_beta: 'customBetaRegion',
      custom_gamma: 'customGammaRegion',
      custom_delta: 'customDeltaRegion'
    };
    localStorage.setItem(storageKeys[category as keyof typeof storageKeys], region);
    
    // Clear validation errors for this category when region changes
    setValidationErrors(prev => {
      const newErrors = { ...prev };
      Object.keys(newErrors).forEach(key => {
        if (key.startsWith(category)) {
          delete newErrors[key];
        }
      });
      return newErrors;
    });
    
    // Check if this category has any instance types set
    const categoryKey = category as CategoryType;
    const hasInstanceTypes = Object.values(instanceTypes[categoryKey]).some(value => value !== null);
    
    // If this category has instance types, update prioritizedRegions and checkbox state
    if (hasInstanceTypes) {
      // Remove the previous region from prioritizedRegions if it exists
      if (previousRegion) {
        setPrioritizedRegions(prev => prev.filter(r => r !== previousRegion));
        
        // Also remove from manuallyPrioritizedRegions to uncheck the checkbox
        setManuallyPrioritizedRegions(prev => prev.filter(r => r !== previousRegion));
      }
      
      // Add the new region to prioritizedRegions
      if (region) {
        setPrioritizedRegions(prev => {
          // Only add if not already in the list
          if (!prev.includes(region)) {
            return [...prev, region];
          }
          return prev;
        });
        
        // Also add to manuallyPrioritizedRegions to check the checkbox
        setManuallyPrioritizedRegions(prev => {
          // Only add if not already in the list
          if (!prev.includes(region)) {
            return [...prev, region];
          }
          return prev;
        });
      }
    }
  };

  const { data: regionsList } = useGetProvidersAwsRegions({
    query: {
      queryKey: getGetProvidersAwsRegionsQueryKey(),
      enabled: selectedProvider.option === "AWS",
    },
  });

  const regions = regionsList?.regions || [];

  const [prioritizedRegions, setPrioritizedRegions] = useState<string[]>([]);

  // Add state for manually prioritized regions
  const [manuallyPrioritizedRegions, setManuallyPrioritizedRegions] = useState<string[]>([]);

  const [instanceTypes, setInstanceTypes] = useState<InstanceTypeSelections>({
    small: { primary: null, secondary: null, fallback: null },
    medium: { primary: null, secondary: null, fallback: null },
    large: { primary: null, secondary: null, fallback: null },
    custom_alpha: { primary: null, secondary: null, fallback: null },
    custom_beta: { primary: null, secondary: null, fallback: null },
    custom_gamma: { primary: null, secondary: null, fallback: null },
    custom_delta: { primary: null, secondary: null, fallback: null },
  });

  // Create a filtered version of prioritizedRegions for display
  const displayPrioritizedRegions = useMemo(() => {
    // Get custom regions that have instances set up
    const activeCustomRegions = Object.entries(customRegions)
      .filter(([category, region]) => {
        if (!region) return false;
        const categoryKey = category as CategoryType;
        return Object.values(instanceTypes[categoryKey]).some(value => value !== null);
      })
      .map(([category, region]) => ({
        region,
        category
      }));

    // Create a map of active custom regions for quick lookup
    const activeCustomRegionsMap = new Map(
      activeCustomRegions.map(item => [item.region, item.category])
    );

    // If there are manually prioritized regions, show both manually prioritized and active custom regions
    if (manuallyPrioritizedRegions.length > 0) {
      // Return all prioritized regions, marking those that are custom
      return prioritizedRegions.map(region => ({
        region,
        isCustom: activeCustomRegionsMap.has(region),
        customCategory: activeCustomRegionsMap.get(region)
      }));
    } else {
      // If there are NO manually prioritized regions, don't show any prioritized regions in the UI
      return [];
    }
  }, [prioritizedRegions, customRegions, instanceTypes, manuallyPrioritizedRegions]);


  const { data: cloudInstanceTypes, isPending: isLoadingInstanceTypes } = useQuery({
    queryKey: getGetAwsInstanceTypesQueryKey(selectedRegion),
    queryFn: () => getAwsInstanceTypes(selectedRegion),
    enabled: !!selectedRegion
  });

  // Show warning if any instance types are unavailable
  useEffect(() => {
    const errors: Record<string, boolean> = {};
      cloudInstanceTypes?.validation?.forEach(v => {
        // only standard (non-custom) types here
        if (!v.available && !v.size_alias.startsWith('custom_')) {
          errors[`${v.size_alias}-${v.priority}`] = true;
        }
      });
      setValidationErrors(prev => {
        // preserve any existing custom_* errors
        const custom = Object.entries(prev)
          .filter(([k]) => k.startsWith('custom_'))
          .reduce((acc, [k,v]) => (acc[k]=v, acc), {} as Record<string,boolean>);
        return { ...custom, ...errors };
      });
  }, [cloudInstanceTypes]);

  

  // Load initial values from DB
  useEffect(() => {
    if (cloudInstanceTypes?.mappings) {
      const initialState: InstanceTypeSelections = {
        small: { primary: null, secondary: null, fallback: null },
        medium: { primary: null, secondary: null, fallback: null },
        large: { primary: null, secondary: null, fallback: null },
        custom_alpha: { primary: null, secondary: null, fallback: null },
        custom_beta: { primary: null, secondary: null, fallback: null },
        custom_gamma: { primary: null, secondary: null, fallback: null },
        custom_delta: { primary: null, secondary: null, fallback: null },
      };

      cloudInstanceTypes.mappings.forEach((mapping: InstanceSizeMapping) => {
        const option = mapping.priority === 1 ? 'primary' 
                    : mapping.priority === 2 ? 'secondary' 
                    : 'fallback';
        
        if (initialState[mapping.size_alias as CategoryType]) {
          initialState[mapping.size_alias as CategoryType][option] = mapping.instance_type;
        }
      });

      setInstanceTypes(initialState);
    }
  }, [cloudInstanceTypes]);

   // Update prioritizedRegions when data is loaded
   useEffect(() => {
    if (regionsList?.prioritizedRegions) {
      setPrioritizedRegions(regionsList.prioritizedRegions);
      
      // Initialize manuallyPrioritizedRegions with all prioritizedRegions
      // We'll filter out custom regions with instances later
      setManuallyPrioritizedRegions(regionsList.prioritizedRegions || []);
    }
  }, [regionsList?.prioritizedRegions]);

  // Effect to manage prioritizedRegions based on manuallyPrioritizedRegions and custom regions
  useEffect(() => {
    // Get custom regions that have instances set up
    const activeCustomRegions = Object.entries(customRegions)
      .filter(([category, region]) => {
        if (!region) return false; // Skip empty regions
        
        const categoryKey = category as CategoryType;
        // Check if any instance type is selected for this category
        return Object.values(instanceTypes[categoryKey]).some(value => value !== null);
      })
      .map(([, region]) => region);
  
    // If there are manually prioritized regions, include both manually prioritized and active custom regions
    if (manuallyPrioritizedRegions.length > 0) {
      // Create a new Set with manually prioritized regions first
      const newPrioritizedRegions = new Set(manuallyPrioritizedRegions);
      
      // Then add active custom regions
      activeCustomRegions.forEach(region => newPrioritizedRegions.add(region));
      
      // Convert to array
      const combinedRegions = Array.from(newPrioritizedRegions);
      
      // Only update if there's a change (compare sorted arrays)
      const currentSorted = [...prioritizedRegions].sort();
      const newSorted = [...combinedRegions].sort();
      
      if (JSON.stringify(newSorted) !== JSON.stringify(currentSorted)) {
        setPrioritizedRegions(combinedRegions);
      }
    } else {
      // If there are NO manually prioritized regions, the prioritizedRegions list should be empty
      if (prioritizedRegions.length > 0) {
        setPrioritizedRegions([]);
      }
    }
  }, [manuallyPrioritizedRegions, customRegions, instanceTypes, prioritizedRegions]);

  // Memoize availableInstanceTypes to prevent unnecessary recalculations
  const availableInstanceTypes = useMemo(() => 
    cloudInstanceTypes?.instance_types || [], 
    [cloudInstanceTypes?.instance_types]
  );


  // Memoize the handleInstanceTypeChange function
  const handleInstanceTypeChange = useCallback((
    category: CategoryType,
    option: OptionType,
    value: string | null
  ) => {
    const priority = option === 'primary' ? '1' : option === 'secondary' ? '2' : '3';
    const validationKey = `${category}-${priority}`;
    
    setValidationErrors((prev) => {
      const newErrors = { ...prev };
      delete newErrors[validationKey];
      return newErrors;
    });

    setInstanceTypes((prev) => ({
      ...prev,
      [category]: {
        ...prev[category],
        [option]: value,
      },
    }));
    
    // If this is a custom category, check if we need to update prioritizedRegions and checkbox state
    if (category.startsWith('custom_')) {
      const customCategory = category as keyof CustomRegions;
      const region = customRegions[customCategory];
      
      if (region) {
        // Check if all instance types for this category will be null after this change
        const updatedTypes = {
          ...instanceTypes,
          [category]: {
            ...instanceTypes[category],
            [option]: value
          }
        };
        
        const allNull = Object.values(updatedTypes[category]).every(val => val === null);
        
        if (allNull) {
          // If all instance types are now null, remove the region from prioritizedRegions
          setPrioritizedRegions(prev => prev.filter(r => r !== region));
          
          // Also remove from manuallyPrioritizedRegions to uncheck the checkbox
          setManuallyPrioritizedRegions(prev => prev.filter(r => r !== region));
        } else {
          // If at least one instance type is not null, add the region to prioritizedRegions
          if (!prioritizedRegions.includes(region)) {
            setPrioritizedRegions(prev => [...prev, region]);
          }
          
          // Also add to manuallyPrioritizedRegions to check the checkbox
          if (!manuallyPrioritizedRegions.includes(region)) {
            setManuallyPrioritizedRegions(prev => [...prev, region]);
          }
        }
      }
    }
    
    setHasChanges(true);
  }, [customRegions, instanceTypes, prioritizedRegions, manuallyPrioritizedRegions]);

  const isValidConfiguration = () => {
    return Object.keys(validationErrors).length === 0;
  };

  const setInstanceTypeMutation = useSetInstanceType({
    mutation: {
      onSuccess: () => {
        toast.success("Instance types updated successfully");
        // Invalidate and refetch the instance types query
        queryClient.invalidateQueries({
          queryKey: getGetAwsInstanceTypesQueryKey(selectedRegion)
        });
      }
    }
  });

  const handleSaveSettings = async () => {
    try {
      // Create a map of current mappings for comparison
      const currentMappings = new Map();
      cloudInstanceTypes?.mappings?.forEach((mapping: InstanceSizeMapping) => {
        const key = `${mapping.size_alias}-${mapping.priority}`;
        currentMappings.set(key, mapping.instance_type);
      });

      const changes: MappingChange[] = [];

      // Compare current state with previous state and generate changes
      for (const [sizeAlias, priorities] of Object.entries(instanceTypes)) {
        for (const [priority, instanceType] of Object.entries(priorities)) {
          let priorityNum;
          switch (priority) {
            case 'primary': priorityNum = 1; break;
            case 'secondary': priorityNum = 2; break;
            case 'fallback': priorityNum = 3; break;
            default: continue;
          }

          const key = `${sizeAlias}-${priorityNum}`;
          const existingType = currentMappings.get(key);

          // If there's a new value, add it
          if (instanceType !== null) {
            changes.push({
              provider: selectedProvider.option,
              size_alias: sizeAlias,
              priority: priorityNum,
              instance_type: instanceType,
              action: 'add'
            });
          }
          // If there was an existing value but now it's null, mark it for deletion
          else if (existingType) {
            changes.push({
              provider: selectedProvider.option,
              size_alias: sizeAlias,
              priority: priorityNum,
              instance_type: existingType,
              action: 'delete'
            });
          }
        }
      }

      // Get active custom regions with instances
      const activeCustomRegions = Object.entries(customRegions)
        .filter(([category, region]) => {
          if (!region) return false;
          const categoryKey = category as CategoryType;
          return Object.values(instanceTypes[categoryKey]).some(value => value !== null);
        })
        .map(([, region]) => region);

      // Determine what to send as prioritizedRegions
      let regionsToSave: string[] = [];

      // Check if there are any manually prioritized regions (excluding custom regions)
      const manuallyPrioritizedNonCustomRegions = manuallyPrioritizedRegions.filter(
        region => !activeCustomRegions.includes(region)
      );

      // If there are manually prioritized regions (excluding those from custom instances),
      // include both manually prioritized and active custom regions
      if (manuallyPrioritizedNonCustomRegions.length > 0) {
        // Create a new Set with manually prioritized regions first
        const newPrioritizedRegions = new Set(manuallyPrioritizedRegions);
        
        // Then add active custom regions
        activeCustomRegions.forEach(region => newPrioritizedRegions.add(region));
        
        // Convert to array
        regionsToSave = Array.from(newPrioritizedRegions);
      } else {
        // If there are NO manually prioritized regions (excluding those from custom instances),
        // then the prioritizedRegions list should be empty when saving
        regionsToSave = [];
      }

      await setInstanceTypeMutation.mutateAsync({
        data: {
          changes, 
          prioritizedRegions: regionsToSave,
        }
      });

    } catch (error) {
      toast.error(`Failed to update instance types: ${error instanceof Error ? error.message : 'Please try again.'}`);
    }
  };

  // Setup custom instance type queries based on their respective custom regions
  const alphaQuery = useCustomInstanceTypes('custom_alpha', customRegions.custom_alpha);
  const betaQuery = useCustomInstanceTypes('custom_beta', customRegions.custom_beta);
  const gammaQuery = useCustomInstanceTypes('custom_gamma', customRegions.custom_gamma);
  const deltaQuery = useCustomInstanceTypes('custom_delta', customRegions.custom_delta);

  // Create a mapping of custom type queries for easier access
  const customInstanceTypesQueries = {
    custom_alpha: alphaQuery,
    custom_beta: betaQuery,
    custom_gamma: gammaQuery,
    custom_delta: deltaQuery
  };

  // Validate custom instance types when they change
  useEffect(() => {
    const customTypes: CustomType[] = ['custom_alpha', 'custom_beta', 'custom_gamma', 'custom_delta'];
    
    // Create a new validation errors object
    const newValidationErrors = { ...validationErrors };
    
    // Clear existing custom validation errors
    Object.keys(newValidationErrors).forEach(key => {
      if (key.startsWith('custom_')) {
        delete newValidationErrors[key];
      }
    });
    
    customTypes.forEach(type => {
      const query = customInstanceTypesQueries[type];
      const categoryKey = `${type}` as CategoryType;
      
      // Check if we have validation data
      if (query.data?.validation && query.data.validation.length > 0) { 
        query.data.validation.forEach(v => {
          if (!v.available) {
            const priority = v.priority.toString();
            const option = priority === '1' ? 'primary' : 
                          priority === '2' ? 'secondary' : 'fallback';
            
            // Only set error if this instance type is currently selected
            const selectedValue = instanceTypes[categoryKey][option as OptionType];
            if (selectedValue === v.instance_type) {
              const validationKey = `${categoryKey}-${priority}`;
              newValidationErrors[validationKey] = true;
            }
          }
        });
      } else {
        console.log(`No validation data for ${type}`);
      }
    });
    
    // Update validation errors state
    setValidationErrors(newValidationErrors);
  }, [
    alphaQuery.data, 
    betaQuery.data, 
    gammaQuery.data, 
    deltaQuery.data, 
    instanceTypes
  ]);

  const renderInstanceTypeDropdown = (category: CategoryType, option: OptionType) => {
    const validationKey = `${category}-${option === 'primary' ? '1' : option === 'secondary' ? '2' : '3'}`;
    const hasError = validationErrors[validationKey];
    const queryKey = `${category}-${option}`;
    const currentValue = instanceTypes[category][option];
    
    // IMPORTANT: Memoize this to prevent recalculation on every render
    // This was likely causing the reload when opening the dropdown
    const instanceTypesList = category.startsWith('custom_') 
        ? customInstanceTypesQueries[category as CustomType].data?.instance_types || []
        : availableInstanceTypes;
    
    // IMPORTANT: Memoize the filtered list to prevent recalculation on every render
    const filteredInstanceTypes = (() => {
      const query = typeQueries[queryKey] || '';
      if (!query) return instanceTypesList.slice(0, 100);
      
      const searchTerm = query.toLowerCase();
      return instanceTypesList
        .filter(type => 
          type.type.toLowerCase().includes(searchTerm) || 
          (type.alias && type.alias.toLowerCase().includes(searchTerm))
        )
        .slice(0, 100);
    })();
    
    // IMPORTANT: Memoize the display value function to prevent recalculation on every render
    const getDisplayValue = (instanceTypesList: any[]) => (value: string | null) => {
      if (!value) return '';
      const instance = instanceTypesList.find(t => t.type === value);
      return instance?.alias ? `${instance.alias}` : sanitizeInstanceType(value);
    };

    return (
      <div key={`${category}-${option}`} className="flex flex-col">
        <label className="mb-2 text-sm font-medium capitalize text-gray-700 dark:text-gray-200">
          {option}
        </label>
        <div className="min-h-[20px]">
          {hasError && (
            <div className="text-red-500 text-sm">
              This instance type is not available in {category.startsWith('custom_') ? 
                customRegions[category as keyof CustomRegions] : selectedRegion}
            </div>
          )}
        </div>
        <div className="relative">
          <Combobox
            value={currentValue}
            onChange={(value) => handleInstanceTypeChange(category, option, value)}
          >
            {({ open }) => (
              <div className="relative">
                <div className="relative w-full">
                  <ComboboxInput
                    className={`w-full rounded-md border ${
                      hasError ? 'border-red-500' : 'border-gray-300'
                    } bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 dark:bg-slate-600 dark:border-gray-500 dark:text-white`}
                    displayValue={getDisplayValue(instanceTypesList)}
                    onChange={(event) => setTypeQueries({ 
                      ...typeQueries, 
                      [queryKey]: event.target.value 
                    })}
                    placeholder="Select or search instance type"
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                    {currentValue && (
                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          e.stopPropagation();
                          handleInstanceTypeChange(category, option, null);
                        }}
                        className="mr-2 rounded-full p-1 hover:bg-gray-200 dark:hover:bg-slate-700"
                      >
                        <HiXMark className="h-4 w-4 text-gray-400" />
                      </button>
                    )}
                    <ComboboxButton>
                      <HiChevronDown className="h-5 w-5 text-gray-400" />
                    </ComboboxButton>
                  </div>
                </div>

                {/* Only render options when dropdown is open */}
                {open && (
                  <ComboboxOptions 
                    className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-slate-600"
                    static
                  >
                    <ComboboxOption
                      value={null}
                      className={({ active }) =>
                        `${
                          active
                            ? "bg-purple-600 text-white"
                            : "text-gray-900 dark:text-white"
                        } relative cursor-pointer select-none py-2 px-3`
                      }
                    >
                      Clear selection
                    </ComboboxOption>
                    {filteredInstanceTypes.map((instanceType) => (
                      <ComboboxOption
                        key={instanceType.type}
                        value={instanceType.type}
                        className={({ active }) =>
                          `${
                            active
                              ? "bg-purple-600 text-white"
                              : "text-gray-900 dark:text-white"
                          } relative cursor-pointer select-none py-2 px-3`
                        }
                      >
                        {instanceType.alias
                          ? `${instanceType.alias}`
                          : sanitizeInstanceType(instanceType.type)}
                      </ComboboxOption>
                    ))}
                  </ComboboxOptions>
                )}
              </div>
            )}
          </Combobox>
        </div>
      </div>
    );
  };

  return (
    <div className="flex h-full w-full">
      
      <ResponsiveSideNav
        toggleSideNav={() => setIsSideNavOpen((prev) => !prev)}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={() => setIsSideNavOpen((prev) => !prev)} />
        <div className="flex h-full flex-col bg-slate-50 p-6 dark:bg-slate-800">
          <BreadCrumbs />
          <div className="flex flex-col space-y-2 py-4">
            <h1 className="text-3xl font-semibold text-black dark:text-white">
              Instance Type Settings
            </h1>
          </div>

          <div className="container mx-auto mt-10 rounded-lg bg-white p-8 shadow-sm dark:bg-[#374357b5]">
            <TabGroup>
              <TabList className="flex space-x-1 rounded-xl bg-purple-900/20 p-1">
                {cloudProviders.map((provider) => (
                  <Tab
                    key={provider.option}
                    className={({ selected }) =>
                      `w-full rounded-lg py-2.5 text-sm font-medium leading-5
                      ${
                        provider.option !== "AWS" 
                          ? "cursor-not-allowed opacity-50" 
                          : "cursor-pointer"
                      }
                      ${
                        selected
                          ? "bg-white text-purple-700 shadow dark:bg-purple-700 dark:text-white"
                          : "text-gray-600 hover:bg-white/[0.12] hover:text-purple-600 dark:text-gray-200"
                      }`
                    }
                    onClick={() => {
                      if (provider.option === "AWS") {
                        setSelectedProvider(provider);
                      }
                    }}
                    disabled={provider.option !== "AWS"}
                  >
                    <div className="flex items-center justify-center space-x-2">
                      {getProviderTabIcon(provider.option)}
                      <span className="hidden md:inline">{provider.description}</span>
                      <span className="md:hidden">{provider.option}</span>
                    </div>
                  </Tab>
                ))}
              </TabList>
            </TabGroup>

            {selectedProvider.option === "AWS" && (
              <div className="mb-4 mt-2.5">
                <div className="flex flex-col md:flex-row md:justify-between md:items-center mb-2 gap-1.5">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-200">
                    Reference Region
                  </label>
                  {displayPrioritizedRegions.length > 0 && (
                    <div className="flex items-center text-sm text-gray-600 dark:text-gray-300">
                      <span className="flex items-center">
                        
                        <span>Only these regions will be selectable during cloud instance creation.</span>
                      </span>
                    </div>
                  )}
                </div>

                <div className="relative">
                  <div className="flex flex-col md:flex-row md:items-center gap-2">
                    <div className="relative w-full md:w-1/3">
                      <div className="flex items-center">
                        <button
                          type="button"
                          className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 dark:bg-slate-600 dark:border-gray-500 dark:text-white"
                          onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                        >
                          {selectedRegion || "Select a region"}
                          <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                            <HiChevronDown className="h-5 w-5 text-gray-400" />
                          </div>
                        </button>
                        {isLoadingInstanceTypes && (
                          <div className="ml-2">
                            <ImSpinner8 className="h-5 w-5 text-purple-600 animate-spin" />
                          </div>
                        )}
                      </div>
                      
                      {isDropdownOpen && (
                        <div className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-slate-600">
                          <div className="p-2">
                            <div className="mb-2 text-xs text-gray-500 dark:text-gray-300 flex items-center">
                              <HiArrowUp className="h-4 w-4 text-green-500 mr-1" />
                              Check to prioritize regions
                            </div>
                            <input
                              type="text"
                              className="w-full rounded-md border border-gray-300 bg-white py-1 px-2 text-sm focus:outline-none focus:ring-1 focus:ring-purple-500 dark:bg-slate-700 dark:border-gray-500 dark:text-white"
                              placeholder="Search regions..."
                              onChange={(e) => setTypeQueries({ 
                                ...typeQueries, 
                                'reference-region': e.target.value 
                              })}
                            />
                          </div>
                          <ul>
                            {regions
                              .filter(region => 
                                !typeQueries['reference-region'] || 
                                region.toLowerCase().includes((typeQueries['reference-region'] || '').toLowerCase())
                              )
                              .map((region) => (
                                <li
                                  key={region}
                                  className={`relative cursor-pointer select-none py-2 px-3 hover:bg-purple-100 dark:hover:bg-slate-700 ${
                                    selectedRegion === region ? "bg-purple-600 text-white hover:bg-purple-700" : "text-gray-900 dark:text-white"
                                  }`}
                                  onClick={() => {
                                    setSelectedRegion(region);
                                    localStorage.setItem('selectedInstanceTypeRegion', region);
                                    // Don't close dropdown when selecting
                                  }}
                                >
                                  <div className="flex items-center">
                                    <div 
                                      className="mr-2 h-4 w-4 border border-gray-300 rounded flex items-center justify-center bg-white dark:bg-slate-700"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        // Toggle prioritization
                                        if (manuallyPrioritizedRegions.includes(region)) {
                                          // Remove from manually prioritized regions
                                          setManuallyPrioritizedRegions(prev => {
                                            const updated = prev.filter(r => r !== region);
                                            return updated;
                                          });
                                        } else {               
                                          // Add to manually prioritized regions
                                          setManuallyPrioritizedRegions(prev => {
                                            const updated = [...prev, region];
                                            return updated;
                                          });
                                        }
                                      }}
                                    >
                                      {manuallyPrioritizedRegions.includes(region) && (
                                        <HiArrowUp className="h-3 w-3 text-green-600" />
                                      )}
                                    </div>
                                    {region}
                                  </div>
                                </li>
                              ))}
                          </ul>
                        </div>
                      )}
                      
                    </div>
                    
                    {/* Display prioritized regions inline with dropdown */}
                    <div className="md:flex-1 md:flex md:justify-end">
                      <div className="flex flex-wrap gap-2 items-center min-h-10">
                        {displayPrioritizedRegions.map((item) => (
                          <div 
                            key={item.region}
                            className={`flex items-center rounded-full px-3 py-1.5 text-sm ${
                              item.isCustom 
                                ? 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-100' 
                                : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100'
                            }`}
                          >
                            {item.isCustom ? (
                              <div className="flex items-center">
                                <span className="mr-1.5 text-xs font-semibold">
                                  {item.customCategory?.split('_').map(word => 
                                    word.charAt(0).toUpperCase() + word.slice(1)
                                  ).join(' ')}:
                                </span>
                                <span>{item.region}</span>
                              </div>
                            ) : (
                              <div className="flex items-center">
                                <HiArrowUp className="h-3 w-3 mr-1.5 self-center" />
                                <span>{item.region}</span>
                              </div>
                            )}
                            <button
                              type="button"
                              className={`ml-2 h-4 w-4 rounded-full flex items-center justify-center ${
                                item.isCustom 
                                  ? 'hover:bg-purple-200 dark:hover:bg-purple-800' 
                                  : 'hover:bg-green-200 dark:hover:bg-green-800'
                              }`}
                              onClick={() => {
                                // If it's a custom region with instances, we can't remove it from prioritizedRegions
                                // We can only remove it from manuallyPrioritizedRegions
                                if (item.isCustom) {
                                  // Only remove from manuallyPrioritizedRegions if it was manually added
                                  if (manuallyPrioritizedRegions.includes(item.region)) {
                                    setManuallyPrioritizedRegions(prev => 
                                      prev.filter(r => r !== item.region)
                                    );
                                  }
                                } else {
                                  // For regular regions, remove from manuallyPrioritizedRegions
                                  setManuallyPrioritizedRegions(prev => 
                                    prev.filter(r => r !== item.region)
                                  );
                                }
                              }}
                            >
                              <HiXMark className="h-3 w-3" />
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                  
                  {/* Remove the information text from here since it's moved up */}
                  
                  {/* Click outside handler to close dropdown */}
                  {isDropdownOpen && (
                    <div 
                      className="fixed inset-0 z-0" 
                      onClick={() => setIsDropdownOpen(false)}
                    />
                  )}
                </div>
              </div>
            )}

            <div className="mt-8 space-y-8">
              {(["small", "medium", "large", "custom_alpha", "custom_beta", "custom_gamma", "custom_delta"] as CategoryType[]).map((category) => (
                <div key={category} className={`rounded-lg bg-gray-50 p-6 dark:bg-slate-700 ${category.startsWith('custom_') ? 'border-2 border-purple-700' : ''}`}>
                  <h3 className="mb-4 text-xl font-semibold capitalize text-gray-900 dark:text-white">
                    {category.replace('_', ' ')} Instances
                  </h3>
                  
                  {/* Add custom region dropdown for custom categories */}
                  {category.startsWith('custom_') && (
                    <div className="mb-4">
                      <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-200">
                        Region for {category.replace('custom_', '')}
                      </label>
                      <div className="flex items-center gap-2">
                        <div className="relative w-full md:w-1/3">
                          <Combobox
                            value={customRegions[category as keyof CustomRegions]}
                            onChange={(region: string | null) => {
                              // Default to the reference region if null is selected
                              const newRegion = region || selectedRegion;
                              handleCustomRegionChange(category as keyof CustomRegions, newRegion);
                            }}
                          >
                            <div className="relative">
                              <div className="relative w-full">
                                <ComboboxInput
                                  className="w-full rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:outline-none focus:ring-1 focus:ring-purple-500 dark:bg-slate-600 dark:border-gray-500 dark:text-white"
                                  displayValue={(region: string) => region || "Select a region"}
                                  onChange={(event) => setTypeQueries({ 
                                    ...typeQueries, 
                                    [`${category}-region`]: event.target.value 
                                  })}
                                  placeholder="Select or search region"
                                />
                                <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                                  {customRegions[category as keyof CustomRegions] && (
                                    <button
                                      onClick={(e) => {
                                        e.preventDefault();
                                        e.stopPropagation();
                                        // Set to reference region when clearing
                                        handleCustomRegionChange(category as keyof CustomRegions, selectedRegion);
                                      }}
                                      className="mr-2 rounded-full p-1 hover:bg-gray-200 dark:hover:bg-slate-700"
                                    >
                                      <HiXMark className="h-4 w-4 text-gray-400" />
                                    </button>
                                  )}
                                  <ComboboxButton>
                                    <HiChevronDown className="h-5 w-5 text-gray-400" />
                                  </ComboboxButton>
                                </div>
                              </div>
                              <ComboboxOptions 
                                className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none dark:bg-slate-600"
                              >
                                {regions
                                  .filter(region => 
                                    !typeQueries[`${category}-region`] || 
                                    region.toLowerCase().includes((typeQueries[`${category}-region`] || '').toLowerCase())
                                  )
                                  .map((region) => (
                                    <ComboboxOption
                                      key={region}
                                      value={region}
                                      className={({ active }) =>
                                        `${
                                          active
                                            ? "bg-purple-600 text-white"
                                            : "text-gray-900 dark:text-white"
                                        } relative cursor-pointer select-none py-2 px-3`
                                      }
                                    >
                                      {region}
                                    </ComboboxOption>
                                  ))}
                              </ComboboxOptions>
                            </div>
                          </Combobox>
                        </div>
                        <div className="w-5 h-5 flex items-center">
                          {category.startsWith('custom_') && 
                           customInstanceTypesQueries[category as CustomType].isPending && (
                            <ImSpinner8 className="h-5 w-5 text-purple-600 animate-spin" />
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                    {(["primary", "secondary", "fallback"] as OptionType[]).map((option) => 
                      renderInstanceTypeDropdown(category, option)
                    )}
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6">
              <button
                onClick={handleSaveSettings}
                disabled={!isValidConfiguration()}
                className={`px-4 py-2 rounded-md cursor-pointer ${
                  isValidConfiguration()
                    ? 'bg-purple-600 text-white hover:bg-purple-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Save Changes
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
