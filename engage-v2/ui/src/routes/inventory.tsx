import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";

import {
  getGetInventoryCloudInstancesQueryKey,
  getGetInventoryDomainsQueryKey,
  getGetInventoryEmailAddressesQueryKey,
  getGetInventoryHostsQueryKey,
  getGetInventoryPersonsQueryKey,
  getGetInventoryQueryOptions,
  getGetInventoryUrlsQueryKey,
  useGetInventoryCloudInstances,
  useGetInventoryDomains,
  useGetInventoryEmailAddresses,
  useGetInventoryHosts,
  useGetInventoryPersons,
  useGetInventoryUrls,
} from "../client";
import BreadCrumbs from "../components/BreadCrumbs";
import DomainStatusLegend from "../components/DomainStatusLegend";
import ErrorPage from "../components/ErrorHandling/ErrorPage";
import NotFound from "../components/ErrorHandling/NotFound";
import EditNodeModal from "../components/GraphView/EditNodeModal";
import Header from "../components/Header";
import { cloud_instance_columns } from "../components/InventoryCloudInstanceColumn";
import { TableRow, columns } from "../components/InventoryColumns";
import { inventoryDomainHistoryColumns } from "../components/InventoryDomainHistoryColumn";
import { inventoryEmailAddressColumns } from "../components/InventoryEmailAddressColumn";
import { hosts_columns } from "../components/InventoryHostColumn";
import { inventoryPersonColumns } from "../components/InventoryPersonColumns";
import { urlColumns } from "../components/InventoryUrlColumns";
import ResponsiveSideNav from "../components/ResponsiveSideNav";
import Table from "../components/Table";
import { NodeEngagementGroup } from "../model";
import { errorCode } from "../utils/assets";

// Remove the sampleDomains array

export const Route = createFileRoute("/inventory")({
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
  component: Inventory,
  loader: async ({ context: { queryClient } }) => {
    return queryClient.ensureQueryData(getGetInventoryQueryOptions());
  },
});

function Inventory() {
  const nodeDetails = Route.useLoaderData();
  const { node_types: nodeTypes, node_groups: nodeGroups } = nodeDetails || [];
  const [activeTab, setActiveTab] = useState("all-nodes");
  const [isOpenEditModal, setIsOpenEditModal] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<TableRow | null>(null);
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  const tabs = [
    { id: "all-nodes", label: "All Nodes" },
    { id: "cloud-instance", label: "Cloud Instance" },
    { id: "host", label: "Host" },
    { id: "url", label: "URL" },
    { id: "person", label: "Person" },
    { id: "email-address", label: "Email Address" },
    { id: "domain", label: "Domain" },
  ];

  const { data: cloudInstancesList } = useGetInventoryCloudInstances({
    query: {
      queryKey: getGetInventoryCloudInstancesQueryKey(),
      enabled: activeTab === "cloud-instance",
    },
  });
  const cloud_instances = cloudInstancesList?.cloud_instances || [];

  const { data: hostList } = useGetInventoryHosts({
    query: {
      queryKey: getGetInventoryHostsQueryKey(),
      enabled: activeTab === "host",
    },
  });
  const hosts = hostList?.hosts || [];

  const { data: emailAddressesList } = useGetInventoryEmailAddresses({
    query: {
      queryKey: getGetInventoryEmailAddressesQueryKey(),
      enabled: activeTab === "email-address",
    },
  });
  const email_addresses = emailAddressesList?.email_addresses || [];

  const { data: personsList } = useGetInventoryPersons({
    query: {
      queryKey: getGetInventoryPersonsQueryKey(),
      enabled: activeTab === "person",
    },
  });
  const persons = personsList?.persons || [];

  const { data: urlList } = useGetInventoryUrls({
    query: {
      queryKey: getGetInventoryUrlsQueryKey(),
      enabled: activeTab === "url",
    },
  });
  const urls = urlList?.urls || [];

  // Add the domains API call
  const { data: domainsList } = useGetInventoryDomains({
    query: {
      queryKey: getGetInventoryDomainsQueryKey(),
      enabled: activeTab === "domain",
    },
  });

  const openEditModal = (row: TableRow) => {
    setSelectedRow(row);
    setIsOpenEditModal(true);
  };

  const openEditModalForNodeEngagementGroup = (row: NodeEngagementGroup) => {
    setSelectedRow(row);
    setIsOpenEditModal(true);
  };

  function closeEditModal() {
    setSelectedRow(null);
    setIsOpenEditModal(false);
  }

  return (
    <div className="flex h-full w-full">
      <ResponsiveSideNav
        toggleSideNav={toggleSideNav}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={toggleSideNav} />
        <div className="flex h-full flex-col bg-slate-50 px-3 py-6 md:px-6 lg:px-8 dark:bg-slate-800">
          <div className="w-full">
            <BreadCrumbs />
            <div
              id="inventory-page-main-title"
              className="flex flex-col space-y-3 pt-6 pb-2"
            >
              <span className="text-3xl font-semibold text-black dark:text-white">
                Inventory
              </span>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                View and search through your inventory items
              </p>
            </div>

            <div className="mt-8">
              <div className="rounded-xl border border-gray-200 bg-white shadow-lg dark:border-gray-600 dark:bg-[#374357]">
                <div className="border-b border-gray-200 dark:border-gray-600">
                  <nav className="-mb-px flex overflow-x-auto px-6">
                    {tabs.map((tab) => (
                      <button
                        key={tab.id}
                        onClick={() => setActiveTab(tab.id)}
                        className={`cursor-pointer border-b-2 px-4 py-4 text-sm font-medium whitespace-nowrap transition-colors duration-200 ${
                          activeTab === tab.id
                            ? "border-purple-600 text-purple-600 dark:border-purple-400 dark:text-purple-400"
                            : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
                        }`}
                      >
                        {tab.label}
                      </button>
                    ))}
                  </nav>
                </div>

                <div className="p-6">
                  <div className="w-full overflow-x-auto">
                    <div className="min-h-[500px]">
                      {cloud_instances && activeTab === "cloud-instance" && (
                        <Table
                          data={cloud_instances}
                          columns={cloud_instance_columns(openEditModal)}
                        />
                      )}
                      {nodeTypes && activeTab === "all-nodes" && (
                        <Table
                          data={nodeGroups ?? []}
                          columns={
                            nodeGroups
                              ? columns(openEditModalForNodeEngagementGroup)
                              : []
                          }
                        />
                      )}
                      {hosts && activeTab === "host" && (
                        <Table
                          data={hosts}
                          columns={hosts_columns(openEditModal)}
                        />
                      )}
                      {email_addresses && activeTab === "email-address" && (
                        <Table
                          data={email_addresses}
                          columns={inventoryEmailAddressColumns(openEditModal)}
                        />
                      )}
                      {persons && activeTab === "person" && (
                        <Table
                          data={persons}
                          columns={inventoryPersonColumns(openEditModal)}
                        />
                      )}
                      {urls && activeTab === "url" && (
                        <Table
                          data={urls}
                          columns={urlColumns(openEditModal)}
                        />
                      )}
                      {/* Domains Tab */}
                      {activeTab === "domain" && (
                        <div className="flex h-full flex-col">
                          <div className="mb-4 flex items-center justify-between">
                            <DomainStatusLegend />
                            <div className="flex space-x-2">
                              {/* Existing buttons/controls */}
                            </div>
                          </div>

                          <div className="flex-1 overflow-hidden">
                            <div className="h-full overflow-x-auto">
                              <div className="min-h-[500px] min-w-[800px]">
                                <Table
                                  data={domainsList?.domains || []}
                                  columns={inventoryDomainHistoryColumns} // No parameters = no actions column
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <EditNodeModal
              isOpen={isOpenEditModal}
              closeModal={closeEditModal}
              nodeDetails={selectedRow}
              isNodeGraph={false}
              engagementName={
                selectedRow && "engagement_name" in selectedRow
                  ? selectedRow.engagement_name
                  : ""
              }
            />
          </div>
        </div>
      </div>
    </div>
  );
}
