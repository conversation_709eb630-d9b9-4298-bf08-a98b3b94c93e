import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";

import {
  getDeployments,
  getGetDeploymentsQueryKey,
  getGetDeploymentsQueryOptions,
} from "../client";
import { DeploymentColumns } from "../components/Deployments/DeploymentColumn";
import ErrorPage from "../components/ErrorHandling/ErrorPage";
import NotFound from "../components/ErrorHandling/NotFound";
import NoFilterTable from "../components/NoFilterTable";
import { DeploymentModel } from "../model";
import { errorCode } from "../utils/assets";
import { createRefetchStatus } from "../utils/refetchEngagementsStatus";

export const Route = createFileRoute("/deployments/")({
  component: DeploymentsIndexComponent,
  loader: async ({ context: { queryClient } }) => {
    return queryClient.ensureQueryData(getGetDeploymentsQueryOptions());
  },
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
});

function DeploymentsIndexComponent() {
  const userQueryKey = getGetDeploymentsQueryKey();
  const userQueryFn = () => getDeployments();
  const { data: deploymentDetails } = useSuspenseQuery({
    queryKey: userQueryKey,
    queryFn: userQueryFn,
    refetchInterval: createRefetchStatus<DeploymentModel>("deployments", {
      refetchIntervalSeconds: 5000,
      statusField: "status",
    }),
  });

  const deployments = deploymentDetails?.deployments || [];

  const tabs = [
    { id: "all", label: "All Deployments" },
    { id: "completed", label: "Completed" },
  ];
  const [activeTab, setActiveTab] = useState("all");

  const filteredDeployments =
    activeTab === "completed"
      ? deployments.filter(
          (deployment: DeploymentModel) =>
            deployment.status === "SUCCESS" || deployment.status === "WARNING",
        )
      : deployments;

  return (
    <div className="container mx-auto mt-20">
      <div className="rounded-lg bg-white shadow-sm dark:bg-[#374357b5]">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`cursor-pointer border-b-2 p-3 text-center text-sm md:w-1/4 md:px-1 md:py-4 ${
                  activeTab === tab.id
                    ? "border-purple-600 text-purple-500 dark:border-purple-400 dark:text-purple-300"
                    : "border-transparent hover:border-gray-300 hover:text-gray-800 dark:text-white dark:hover:font-semibold dark:hover:text-gray-200"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
        <div className="rounded-lg bg-white p-3 shadow-sm dark:bg-[#374357b5] dark:text-white">
          <NoFilterTable
            data={filteredDeployments}
            columns={DeploymentColumns}
          />
        </div>
      </div>
    </div>
  );
}
