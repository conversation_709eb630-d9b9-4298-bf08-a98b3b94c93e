@import "tailwindcss";

@config '../tailwind.config.js';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

.card {
  background: var(--surface-card);

  border-radius: 10px;
  margin-bottom: 1rem;
}

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #e2e8f0;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #535e6c;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 5px;
  }
}

/* PrimeReact Calendar responsive styles */
.p-calendar {
  width: 100% !important;
}

.p-calendar .p-inputtext {
  width: 100% !important;
  min-width: 0 !important;
  font-size: 12px !important;
}

/* Mobile responsive calendar panel */
@media (max-width: 768px) {
  .p-calendar-panel {
    font-size: 12px !important;
    transform: scale(0.9) !important;
    transform-origin: top left !important;
  }

  .p-datepicker {
    font-size: 11px !important;
  }

  .p-datepicker table td {
    padding: 0.25rem !important;
  }

  .p-datepicker .p-datepicker-header {
    padding: 0.5rem !important;
  }
}

/* Ensure calendar panels don't overflow on small screens */
@media (max-width: 640px) {
  .p-calendar-panel {
    max-width: 280px !important;
    transform: scale(0.85) !important;
  }

  /* Adjust table filter containers on mobile */
  .table-filter-container {
    min-width: 120px !important;
    max-width: 150px !important;
  }
}

/* Extra small screens */
@media (max-width: 480px) {
  .p-calendar-panel {
    max-width: 260px !important;
    transform: scale(0.8) !important;
  }

  .p-calendar .p-inputtext {
    font-size: 11px !important;
    padding: 0.25rem !important;
  }

  /* Make table headers more compact on very small screens */
  .table-header-compact {
    padding: 0.25rem !important;
    font-size: 11px !important;
  }
}
