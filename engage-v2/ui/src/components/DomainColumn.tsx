import { createColumnHelper } from "@tanstack/react-table";
import { useState } from "react";
import { HiOutlineGlobe } from "react-icons/hi";
import { HiCheck, HiPencil, HiXMark } from "react-icons/hi2";

import { DomainResponse } from "../model";

const columnHelper = createColumnHelper<DomainResponse>();

// Inline editable cell component
const EditableCell = ({
  value,
  onSave,
  type = "text",
  icon,
}: {
  value: string;
  onSave: (newValue: string) => void;
  type?: "text" | "date";
  icon?: React.ReactNode;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value);

  const handleSave = () => {
    onSave(editValue);
    setIsEditing(false);
  };

  const handleCancel = () => {
    setEditValue(value);
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSave();
    } else if (e.key === "Escape") {
      handleCancel();
    }
  };

  if (isEditing) {
    return (
      <div className="flex min-w-[120px] items-center space-x-1 py-2">
        {icon && <span className="flex-shrink-0">{icon}</span>}
        <input
          type={type}
          value={editValue}
          onChange={(e) => setEditValue(e.target.value)}
          onKeyDown={handleKeyDown}
          onBlur={handleSave}
          autoFocus
          className="flex-1 rounded border border-gray-300 px-2 py-1 text-xs focus:ring-2 focus:ring-blue-500 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
        />
        <button
          onClick={handleSave}
          className="p-1 text-green-600 hover:text-green-800"
        >
          <HiCheck className="h-3 w-3" />
        </button>
        <button
          onClick={handleCancel}
          className="p-1 text-red-600 hover:text-red-800"
        >
          <HiXMark className="h-3 w-3" />
        </button>
      </div>
    );
  }

  return (
    <div className="group flex min-w-[120px] items-center space-x-1 py-2">
      {icon && <span className="flex-shrink-0">{icon}</span>}
      <span className="flex-1 truncate text-sm">{value || "-"}</span>
      <button
        onClick={() => setIsEditing(true)}
        className="p-1 text-gray-400 opacity-0 transition-opacity group-hover:opacity-100 hover:text-gray-600"
      >
        <HiPencil className="h-3 w-3" />
      </button>
    </div>
  );
};

export const domainColumns = ({
  onAssign,
  onBurn,
  onUpdate,
  onExpire,
}: {
  onAssign?: (domain: DomainResponse) => void;
  onBurn?: (domainId: string) => void;
  onUpdate?: (
    domainId: string,
    field: keyof DomainResponse,
    value: string,
    domainStatus?: string,
  ) => void;
  onExpire?: (domainId: string) => void;
}) => {
  console.log("Creating domain columns with handlers:", {
    hasAssignHandler: !!onAssign,
    hasBurnHandler: !!onBurn,
    hasUpdateHandler: !!onUpdate,
    hasExpireHandler: !!onExpire,
  });

  // Create base columns
  const baseColumns = [
    columnHelper.accessor(
      (row) => {
        return row.url || "";
      },
      {
        id: "url",
        header: () => "Domain",
        cell: (info) => {
          const value = info.getValue();
          return (
            <EditableCell
              value={value}
              onSave={(newValue) => {
                const id = info.row.original.id;
                onUpdate?.(id, "url", newValue);
              }}
              icon={<HiOutlineGlobe className="h-4 w-4 text-blue-500" />}
            />
          );
        },
      },
    ),
    columnHelper.accessor(
      (row) => {
        return row.registrar || "";
      },
      {
        id: "registrar",
        header: () => "Registrar",
        cell: (info) => {
          const value = info.getValue();
          return (
            <EditableCell
              value={value}
              onSave={(newValue) => {
                const id = info.row.original.id;
                onUpdate?.(id, "registrar", newValue);
              }}
            />
          );
        },
      },
    ),
    columnHelper.accessor(
      (row) => {
        return row.purchase_date || "";
      },
      {
        id: "purchase_date",
        header: () => "Purchase Date",
        cell: (info) => {
          const value = info.getValue();
          return (
            <EditableCell
              value={value}
              onSave={(newValue) => {
                const id = info.row.original.id;
                onUpdate?.(id, "purchase_date", newValue);
              }}
              type="date"
            />
          );
        },
      },
    ),
    columnHelper.accessor(
      (row) => {
        return row.renewal_date || "";
      },
      {
        id: "renewal_date",
        header: () => "Renewal Date",
        cell: (info) => {
          const value = info.getValue();
          return (
            <EditableCell
              value={value}
              onSave={(newValue) => {
                const id = info.row.original.id;
                onUpdate?.(id, "renewal_date", newValue);
              }}
              type="date"
            />
          );
        },
      },
    ),
    columnHelper.accessor(
      (row) => {
        return row.domain_status_enum || "UNASSIGNED";
      },
      {
        id: "domain_status_enum",
        header: () => "Status",
        cell: (info) => {
          const status = info.getValue();
          let statusClass = "";

          switch (status) {
            case "ASSIGNED":
              statusClass = "bg-green-100 text-green-800";
              break;
            case "UNASSIGNED":
              statusClass = "bg-gray-100 text-gray-800";
              break;
            case "BURNED":
              statusClass = "bg-red-100 text-red-800";
              break;
            case "QUARANTINE":
              statusClass = "bg-yellow-100 text-yellow-800";
              break;
            case "EXPIRED":
              statusClass = "bg-blue-100 text-blue-800";
              break;
          }

          return (
            <div className="min-w-[70px] py-2">
              <span
                className={`rounded-full px-2 py-1 text-xs font-medium ${statusClass}`}
              >
                {status}
              </span>
            </div>
          );
        },
      },
    ),
    columnHelper.accessor((row) => row.id, {
      id: "actions",
      header: () => "Actions",
      cell: (info) => {
        const domain = info.row.original;
        const status = domain.domain_status_enum || "UNASSIGNED";

        return (
          <div className="flex space-x-2 py-2 whitespace-nowrap">
            {status === "UNASSIGNED" && (
              <>
                <button
                  onClick={() => onAssign?.(domain)}
                  className="rounded bg-blue-500 px-3 py-1 text-xs text-white hover:bg-blue-600"
                  disabled={!onAssign}
                >
                  Assign
                </button>
                <button
                  onClick={() => onBurn?.(domain.id)}
                  className="rounded bg-red-700 px-3 py-1 text-xs text-white hover:bg-red-800"
                  disabled={!onBurn}
                >
                  Burn
                </button>
              </>
            )}
            {status === "QUARANTINE" && (
              <>
                <button
                  onClick={() => onAssign?.(domain)}
                  className="rounded bg-blue-500 px-3 py-1 text-xs text-white hover:bg-blue-600"
                  disabled={!onAssign}
                >
                  UnAssign
                </button>
                <button
                  onClick={() => onBurn?.(domain.id)}
                  className="rounded bg-red-700 px-3 py-1 text-xs text-white hover:bg-red-800"
                  disabled={!onBurn}
                >
                  Burn
                </button>
              </>
            )}
            {status === "BURNED" && (
              <button
                onClick={() => onExpire?.(domain.id)}
                className="rounded bg-yellow-500 px-3 py-1 text-xs text-white hover:bg-yellow-600"
                disabled={!onExpire}
              >
                Expire
              </button>
            )}
          </div>
        );
      },
    }),
  ];

  return baseColumns;
};
