import { useQueryClient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import Cytoscape from "cytoscape";
import "cytoscape-context-menus/cytoscape-context-menus.css";
import dagre from "cytoscape-dagre";
import {
  ChangeEvent,
  Dispatch,
  MutableRefObject,
  SetStateAction,
  useEffect,
  useRef,
  useState,
} from "react";
import CytoscapeComponent from "react-cytoscapejs";
import { FaFileExport } from "react-icons/fa";
import { HiCheckCircle, HiPencil, HiTable } from "react-icons/hi";
import {
  HiArrowPath,
  HiArrowTrendingDown,
  HiArrowsPointingIn,
  HiArrowsPointingOut,
  HiEllipsisVertical,
  HiMagnifyingGlass,
  HiMagnifyingGlassMinus,
  HiMagnifyingGlassPlus,
  HiOutlineCube,
} from "react-icons/hi2";
import { toast } from "react-toastify";
import "tippy.js/dist/tippy.css";

import {
  getGetEngagementGraphsQuery<PERSON>ey,
  getGetEngagementQueryKey,
  useEditNodeGroup,
} from "../../client";
import { useTheme } from "../../context/ThemeProvider";
import { NodeGroup } from "../../model/nodeGroup.ts";
import "../../style.css";
import {
  chevronRight,
  cloudInstanceSvg,
  emailSvg,
  hostSvg,
  urlSvg,
  userSvg,
} from "../../utils/assets.tsx";
import { addCyInstance, getCyInstance } from "../../utils/cyInstances.ts";
import {
  createContextMenuItem,
  resetNodeHighlights,
} from "../../utils/nodegraphutils";
import CreateNodeModal from "../CreateNode/CreateNodeModal";
import ErrorMessageModal, { ErrorMessage } from "../ErrorMessageModal";
import DeleteNodeModal from "./DeleteNodeModal";
import EditNodeModal from "./EditNodeModal";
import ExistingNodeModal from "./ExisitingNodeModal";

type Props = {
  graphElements: cytoscape.ElementDefinition[];
  nodeGroupID: string;
  nodeGroupName: string;
  onClick: () => void;
  setIsExpanded: Dispatch<SetStateAction<boolean>>;
  isExpanded: boolean;
  resetViewChecked: boolean;
  engagementID: string;
  nodeGroups: NodeGroup[];
  engagementName: string;
};

type ExtendedCore = Cytoscape.Core & {
  contextMenus: any;
  popper: any;
  popperRef: any;
};

const layout = { name: "dagre" };

export default function NodeGroupGraph({
  graphElements,
  nodeGroupID,
  nodeGroupName,
  onClick,
  isExpanded,
  setIsExpanded,
  engagementID,
  nodeGroups,
  engagementName,
}: Props) {
  Cytoscape.use(dagre as unknown as Cytoscape.Ext);
  const queryClient = useQueryClient();

  const [cyInstance, setCyInstance] = useState<Cytoscape.Core | null>(null);
  const [zoomLevel, setZoomLevel] = useState(0);
  const graphRef: MutableRefObject<ExtendedCore | null> =
    useRef<Cytoscape.Core | null>(null);

  const [startNode, setStartNode] = useState<Cytoscape.NodeSingular | null>(
    null,
  );
  const [endNode, setEndNode] = useState<Cytoscape.NodeSingular | null>(null);
  const startNodeRef = useRef<Cytoscape.NodeSingular | null>(null);
  const endNodeRef = useRef<Cytoscape.NodeSingular | null>(null);
  const [showMenu, setShowMenu] = useState(false);
  const menuRef = useRef<HTMLDivElement | null>(null);
  const iconRef = useRef<HTMLImageElement | null>(null);
  const [isHighlightPathEnabled, setIsHighlightPathEnabled] =
    useState<boolean>(false);
  const [isOpen, setIsOpen] = useState<boolean>(false);
  const [isOpenExistingModal, setIsOpenExistingModal] =
    useState<boolean>(false);
  const [isOpenDeleteModal, setIsOpenDeleteModal] = useState<boolean>(false);
  const [direction, setDirection] = useState("");
  const [deleteType, setDeleteType] = useState("");
  const [selectedNode, setSelectedNode] =
    useState<Cytoscape.NodeSingular | null>(null);
  const [selectedDescendants, setSelectedDescendants] = useState<
    string[] | null
  >(null);

  const [selectedEdge, setSelectedEdge] =
    useState<Cytoscape.EdgeSingular | null>(null);

  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [isOpenEditModal, setIsOpenEditModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
  const [isRename, setIsRename] = useState<boolean>(false);
  const [editedName, setEditedName] = useState(nodeGroupName);
  const { isDarkMode } = useTheme();
  function closeCreateModal() {
    setIsOpen(false);
  }

  function openNewNodeModal() {
    setIsOpen(true);
  }

  function openExistingNodeModal(
    event: Cytoscape.EventObject,
    direction: string,
  ) {
    const target = event.target as Cytoscape.NodeSingular;
    if (cyInstance) {
      const bfs = cyInstance.elements().bfs({
        roots: target,
        directed: true,
      });
      const descendantNodes = bfs.path
        .filter((ele) => ele.isNode())
        .map((node) => node.id());

      setSelectedNode(target);
      setSelectedDescendants(descendantNodes);
      setDirection(direction);
      setIsOpenExistingModal(true);
    }
  }

  function closeExistingNodeModal() {
    setIsOpenExistingModal(false);
  }

  function openDeleteNodeModal(
    type: string,
    element?: Cytoscape.NodeSingular | Cytoscape.EdgeSingular,
  ) {
    if (type === "delete" && element?.isNode()) {
      setDeleteType("deleteNode");
      setSelectedNode(element);
    } else if (type === "delete" && element?.isEdge()) {
      setDeleteType("deleteEdge");
      setSelectedEdge(element);
    } else {
      setDeleteType(type);
    }
    setIsOpenDeleteModal(true);
  }

  function closeDeleteNodeModal() {
    setIsOpenDeleteModal(false);
  }

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  function openEditModal(event: Cytoscape.EventObject) {
    const target = event.target as Cytoscape.NodeSingular;
    setSelectedNode(target);
    setIsOpenEditModal(true);
  }

  function closeEditModal() {
    setSelectedNode(null);
    setIsOpenEditModal(false);
  }

  function handleImageClick() {
    setShowMenu((prevShowMenu) => !prevShowMenu);
  }

  function handleMenuItemClick() {
    setIsExpanded((prevIsExpanded) => !prevIsExpanded);
    onClick();
    setShowMenu(false);
  }

  function handleHighlightPathClick() {
    if (startNode && endNode && cyInstance) {
      const result = cyInstance.elements().aStar({
        root: startNode,
        goal: endNode,
        weight: (edge: Cytoscape.EdgeCollection) => {
          return edge.data("weight") || 1;
        },
        directed: true,
      });
      const allNodes = cyInstance.nodes();
      cyInstance.elements().removeClass("highlight");
      allNodes.removeClass("semitransp");

      if (result.path && result.path.length > 0) {
        result.path.forEach((element) => {
          if (element.isEdge()) {
            element.addClass("highlight");
            element.style({
              "line-color": "#FF0000",
              "target-arrow-color": "#FF0000",
              width: 3,
            });
          }
        });
        const pathNodes = new Set(result.path.map((element) => element.id()));
        allNodes.forEach((node) => {
          if (!pathNodes.has(node.id())) {
            node.addClass("semitransp");
          }
        });
      } else {
        openErrorModal({
          title: "Error highlighting kill path",
          message: "Unable to find kill path for the nodes.",
        });
        resetNodeHighlights(cyInstance);
        setStartNode(null);
        setEndNode(null);
      }
    }
  }

  function handleRenameClick() {
    setIsRename((renameStatus) => !renameStatus);
    setShowMenu(false);
  }

  const handleInputChange = (
    event: ChangeEvent<HTMLInputElement>,
    setState: Dispatch<SetStateAction<string>>,
  ) => {
    setState(event.target.value);
  };

  const mutation = useEditNodeGroup({
    mutation: {
      onError: (error: unknown) => {
        if (error instanceof AxiosError && error.response) {
          const errorMessage =
            error.response.data?.errors?.[0]?.message ??
            "An error occurred while editing node group name.";
          toast.error(errorMessage);
        } else {
          toast.error("An unexpected error occurred.");
        }
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Node group name has been successfully edited.");
        }
        const queryKey = getGetEngagementGraphsQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey });
        const engagementQueryKey = getGetEngagementQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey: engagementQueryKey });
      },
    },
  });

  const saveName = () => {
    setIsRename(false);
    const updatedData = {
      name: editedName,
    };
    mutation.mutate({
      nodeGroupID,
      data: updatedData,
    });
  };

  useEffect(() => {
    if (startNode && endNode) {
      setIsHighlightPathEnabled(true);
    } else {
      setIsHighlightPathEnabled(false);
    }
  }, [startNode, endNode]);

  useEffect(() => {
    if (graphElements.length > 0) {
      resetGraph();
    }
  }, [graphElements]);

  useEffect(() => {
    if (cyInstance && !getCyInstance(nodeGroupID)) {
      addCyInstance(nodeGroupID, cyInstance);
    }
    if (cyInstance) {
      const handleDelete = (event: Cytoscape.EventObject) => {
        const node = event.target;
        // Check for all connected edges (both incoming and outgoing)
        const connectedEdges = node.connectedEdges();
        if (connectedEdges.length > 0) {
          openDeleteNodeModal("error");
        } else {
          openDeleteNodeModal("delete", node);
        }
      };

      const createContextMenu = () => {
        const nodeMenuItems = [
          {
            id: "create",
            content: "Create Relationship",
            selector: "node",
            onClickFunction: () => {},
            submenu: [
              {
                id: "inbound",
                content: "Inbound",
                selector: "",
                onClickFunction: (event: any) =>
                  openExistingNodeModal(event, "Inbound"),
              },
              {
                id: "outbound",
                content: "Outbound",
                selector: "",
                onClickFunction: (event: any) =>
                  openExistingNodeModal(event, "Outbound"),
              },
            ],
          },
          {
            id: "info",
            content: "Get Info",
            selector: "node",
            onClickFunction: (event: any) => openEditModal(event),
          },
          {
            id: "delete",
            content: "Delete",
            selector: "node",
            onClickFunction: (event: any) => handleDelete(event),
          },
        ];
        if (startNode && endNode) {
          nodeMenuItems.splice(
            1,
            0,
            createContextMenuItem(
              "start",
              "Start Node",
              "#059669",
              startNodeRef,
              setStartNode,
              endNodeRef,
              setEndNode,
            ),
          );
        } else if (startNode) {
          nodeMenuItems.splice(
            1,
            0,
            createContextMenuItem(
              "end",
              "End Node",
              "#BE123C",
              endNodeRef,
              setEndNode,
              null,
            ),
          );
        } else {
          nodeMenuItems.splice(
            1,
            0,
            createContextMenuItem(
              "start",
              "Start Node",
              "#059669",
              startNodeRef,
              setStartNode,
              endNodeRef,
              setEndNode,
            ),
          );
        }

        cyInstance.contextMenus({
          menuItems: nodeMenuItems,
          menuItemClasses: ["custom-menu-item", "custom-menu-item:hover"],
          contextMenuClasses: ["custom-context-menu"],
          submenuIndicator: {
            src: chevronRight,
            width: 8,
            height: 8,
          },
        });
      };

      const createBackgrounContextMenu = (
        cyInstance: cytoscape.Core,
        x: number,
        y: number,
      ) => {
        cyInstance.contextMenus("removeAll");
        const menuItem = [
          {
            id: "createnewnode",
            content: "Create New Node",
            selector: "core",
            onClickFunction: openNewNodeModal,
          },
        ];

        cyInstance.contextMenus({
          menuItems: menuItem,
          menuItemClasses: isDarkMode
            ? ["dark", "custom-dark-menu-item", "custom-dark-menu-item:hover"]
            : ["custom-menu-item", "custom-menu-item:hover"],
          contextMenuClasses: isDarkMode
            ? ["dark", "custom-dark-context-menu-background"]
            : ["custom-context-menu-background"],
        });
        setTimeout(() => {
          const menuContainer = document.querySelector(
            isDarkMode
              ? ".dark .custom-dark-context-menu-background"
              : ".custom-context-menu-background",
          ) as HTMLElement;
          const cyContainer = cyInstance.container();

          if (menuContainer && cyContainer) {
            cyContainer.appendChild(menuContainer);
            const containerRect = cyContainer.getBoundingClientRect();
            const relativeX = x - containerRect.left;
            const relativeY = y - containerRect.top;

            menuContainer.style.position = "absolute";
            menuContainer.style.top = `${relativeY}px`;
            menuContainer.style.left = `${relativeX}px`;
            menuContainer.style.display = "block";
          }
        }, 100);
      };
      //right-click background
      cyInstance.on("cxttap", function (event) {
        event.preventDefault();
        event.stopPropagation();

        const evtTarget = event.target;
        if (evtTarget === cyInstance) {
          const mouseX = event.originalEvent.clientX;
          const mouseY = event.originalEvent.clientY;
          createBackgrounContextMenu(cyInstance, mouseX, mouseY);

          setTimeout(() => {
            isDarkMode
              ? ".dark .custom-dark-context-menu-background"
              : ".custom-context-menu-background";
          }, 100);
        }

        setTimeout(() => {
          isDarkMode
            ? ".dark .custom-dark-context-menu-background"
            : ".custom-context-menu-background";
        }, 100);
      });
      //right-click node
      cyInstance.on(
        "cxttapstart",
        "node",
        function (event: Cytoscape.EventObject) {
          if (event.target.isNode()) {
            createContextMenu();
          }
        },
      );
      cyInstance.on("dragfree", "node", () => {
        const highlightedEdges = cyInstance.elements(".highlight");
        highlightedEdges.forEach((element) => {
          element.style({
            "line-color": "#FF0000",
            "target-arrow-color": "#FF0000",
            width: 3,
          });
        });
      });

      return () => {
        cyInstance.off("cxttapstart", "node");
        cyInstance.off("cxttap");
        cyInstance.off("drag", "node");
        cyInstance.off("drag", "edge");
      };
    }
  }, [
    cyInstance,
    startNode,
    endNode,
    startNodeRef,
    endNodeRef,
    isDarkMode,
    nodeGroupID,
  ]);

  useEffect(() => {
    if (cyInstance) {
      const handleDeleteEdge = (edge: Cytoscape.EdgeSingular) => {
        setSelectedEdge(edge);
        setDeleteType("deleteEdge");
        setIsOpenDeleteModal(true);
      };

      const createEdgeContextMenu = () => {
        const edgeMenuItems = [
          {
            id: "delete",
            content: "Delete Edge",
            selector: "edge",
            onClickFunction: (event: Cytoscape.EventObject) => {
              const edge = event.target as Cytoscape.EdgeSingular;
              handleDeleteEdge(edge);
            },
          },
        ];

        cyInstance.contextMenus({
          menuItems: edgeMenuItems as unknown as any,
          menuItemClasses: ["custom-menu-item", "custom-menu-item:hover"],
          contextMenuClasses: ["custom-context-menu"],
          submenuIndicator: {
            src: chevronRight,
            width: 8,
            height: 8,
          },
        });
      };

      // Right-click edge to trigger context menu
      cyInstance.on(
        "cxttapstart",
        "edge",
        function (event: Cytoscape.EventObject) {
          if (event.target.isEdge()) {
            createEdgeContextMenu();
          }
        },
      );

      return () => {
        cyInstance.off("cxttapstart", "edge");
      };
    }
  }, [cyInstance]);

  function exportGraph() {
    const cy = graphRef.current;
    if (cy) {
      const pngData = cy.png({
        full: true,
        scale: 2,
      });
      const downloadLink = document.createElement("a");
      downloadLink.href = pngData;
      downloadLink.download = `${nodeGroupName} graph.png`;
      document.body.appendChild(downloadLink);
      downloadLink.click();
      document.body.removeChild(downloadLink);
    }
  }

  function resetGraph() {
    const cy = graphRef.current;
    if (cy) {
      cy.elements().remove();
      cy.json({
        elements: graphElements,
      })
        .layout({
          name: "dagre",
        })
        .run();
      cy.fit();
      cy.center();
      setStartNode(null);
      setEndNode(null);
    }
  }

  function resetZoom() {
    const cy = graphRef.current;
    if (cy) {
      resetNodeHighlights(cy);
      cy.reset();
      cy.fit();
      cy.center();
      setStartNode(null);
      setEndNode(null);
    }
  }

  function increaseZoom() {
    const cy = graphRef.current;
    if (cy) {
      setZoomLevel((prevZoomLevel) => prevZoomLevel + 1);
      cy.fit();
      cy.center();
    }
  }

  function decreaseZoom() {
    const cy = graphRef.current;
    if (cy && zoomLevel > 0) {
      setZoomLevel((prevZoomLevel) => prevZoomLevel - 1);
      cy.fit();
      cy.center();
    }
  }

  function handleClickOutside(event: MouseEvent) {
    if (
      menuRef.current &&
      iconRef.current &&
      !menuRef.current.contains(event.target as Node) &&
      !iconRef.current.contains(event.target as Node)
    ) {
      setShowMenu(false);
    }
  }

  useEffect(() => {
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <div className="rounded-lg border bg-white px-2 py-2 shadow-md dark:border-transparent dark:bg-[#374357b5]">
      <div className="relative flex flex-row justify-between">
        {isRename ? (
          <span className="flex cursor-pointer flex-row items-center space-x-2 rounded-3xl border border-gray-300 px-3 py-1 text-sm drop-shadow-sm">
            <input
              className="w-40 bg-transparent px-3 text-sm focus:outline-hidden dark:text-slate-200"
              value={editedName}
              onChange={(e) => handleInputChange(e, setEditedName)}
              maxLength={50}
            />
            <HiCheckCircle
              className="h-6 w-6 dark:text-slate-200"
              onClick={saveName}
            />
          </span>
        ) : (
          <span className="flex cursor-default flex-row items-center space-x-2 rounded-3xl border border-gray-300 px-3 py-1 text-sm drop-shadow-sm dark:bg-[#374357b5]">
            <HiOutlineCube
              className={`${isDarkMode ? "text-slate-200" : "text-black"} h-6 w-6`}
            />
            <span className="dark:text-slate-200">{nodeGroupName}</span>
          </span>
        )}
        <span ref={iconRef}>
          <HiEllipsisVertical
            onClick={handleImageClick}
            className={`${isDarkMode ? "text-slate-200" : "text-black"} h-7 w-7 cursor-pointer`}
          />
        </span>
        {showMenu && (
          <div
            ref={menuRef}
            className="absolute right-0 z-10 mt-4 w-50 rounded-sm border bg-white shadow-lg"
          >
            <ul>
              <li
                className="hidden cursor-pointer px-4 py-2 hover:bg-gray-100 sm:flex"
                onClick={handleMenuItemClick}
              >
                {isExpanded ? (
                  <div className="flex flex-row items-center space-x-2">
                    <HiArrowsPointingIn className="h-5 w-5" />
                    <span>Reset View</span>
                  </div>
                ) : (
                  <div className="flex flex-row items-center space-x-2">
                    <HiArrowsPointingOut className="h-5 w-5" />
                    <span>Expand View</span>
                  </div>
                )}
              </li>
              <li
                className={`cursor-pointer px-4 py-2 hover:bg-gray-100 ${
                  !isHighlightPathEnabled ? "opacity-50" : ""
                }`}
              >
                <div className="flex flex-row items-center space-x-2">
                  <HiArrowTrendingDown className="h-5 w-5" />
                  <button
                    onClick={handleHighlightPathClick}
                    disabled={!isHighlightPathEnabled}
                  >
                    Highlight Kill Path
                  </button>
                </div>
              </li>
              <li className="cursor-pointer px-4 py-2 hover:bg-gray-100">
                <div className="flex flex-row items-center space-x-2">
                  <HiTable className="h-5 w-5" />
                  <a href={`#table-${nodeGroupID}`}>Show Table View</a>
                </div>
              </li>
              <li
                className="cursor-pointer px-4 py-2 hover:bg-gray-100"
                onClick={handleRenameClick}
              >
                <div className="flex flex-row items-center space-x-2">
                  <HiPencil className="h-5 w-5" />
                  <span>Rename Node Group</span>
                </div>
              </li>
            </ul>
          </div>
        )}
      </div>
      <CreateNodeModal
        engagementID={engagementID}
        nodeGroupID={nodeGroupID}
        isOpen={isOpen}
        closeModal={closeCreateModal}
      />
      <ExistingNodeModal
        isOpen={isOpenExistingModal}
        closeModal={closeExistingNodeModal}
        direction={direction}
        nodeGroups={nodeGroups}
        //selectedNode={selectedNode}
        selectedNode={selectedNode || null}
        descendants={selectedDescendants || null}
        engagementID={engagementID}
        cyInstance={cyInstance}
      />
      <DeleteNodeModal
        isOpen={isOpenDeleteModal}
        closeModal={closeDeleteNodeModal}
        selectedNode={selectedNode || null}
        selectedEdge={selectedEdge}
        deleteType={deleteType}
        engagementID={engagementID}
      />
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
      <EditNodeModal
        isOpen={isOpenEditModal}
        engagementID={engagementID}
        closeModal={closeEditModal}
        nodeDetails={graphElements
          .filter((item) => item.group === "nodes")
          .find((node) => node.data.id === selectedNode?.id())}
        isNodeGraph={true}
        engagementName={engagementName}
      />
      <CytoscapeComponent
        cy={(cy) => {
          graphRef.current = cy as unknown as ExtendedCore;
          setCyInstance(cy as unknown as ExtendedCore);
        }}
        zoomingEnabled={true}
        zoom={zoomLevel}
        stylesheet={[
          {
            selector: "edge",
            style: {
              "curve-style": "bezier",
              "target-arrow-fill": "filled",
              "target-arrow-shape": "triangle",
              "target-arrow-color": "#9CA3AF",
              "line-color": "#9CA3AF",
              width: 1,
            },
          },
          {
            selector: "node.highlight",
            style: {
              "border-color": "#FFF",
              "border-width": 2,
            },
          },
          {
            selector: "node.semitransp",
            style: { opacity: 0.5 },
          },
          {
            selector: "edge.highlight",
            style: { "mid-target-arrow-color": "#FFF" },
          },
          {
            selector: "edge.semitransp",
            style: { opacity: 0.2 },
          },
          {
            selector: 'node[cloud_instance_state = "running"]',
            style: {
              "background-image":
                "data:image/svg+xml;base64," + btoa(cloudInstanceSvg),
              "background-color": "#bae6fd",
            },
          },
          {
            selector: 'node[cloud_instance_state != "running"]',
            style: {
              "background-image":
                "data:image/svg+xml;base64," + btoa(cloudInstanceSvg),
              "background-color": "#656871",
            },
          },
          {
            selector: ".EMAIL_ADDRESS",
            style: {
              "background-image": "data:image/svg+xml;base64," + btoa(emailSvg),
              backgroundColor: "#bbf7d0",
            },
          },

          {
            selector: ".HOST",
            style: {
              "background-image": "data:image/svg+xml;base64," + btoa(hostSvg),
              backgroundColor: "#fecaca",
            },
          },
          {
            selector: ".PERSON",
            style: {
              "background-image": "data:image/svg+xml;base64," + btoa(userSvg),
              backgroundColor: "#fef08a",
            },
          },
          {
            selector: ".URL",
            style: {
              "background-image": "data:image/svg+xml;base64," + btoa(urlSvg),
              backgroundColor: "#e9d5ff",
            },
          },
          {
            selector: "node",
            style: {
              width: "45px",
              height: "45px",
              "font-size": "12px",
              "text-valign": "bottom",
              content: "data(label)",
              "text-halign": "center",
              "border-style": "solid",
              "border-color": "#CDD1D7",
              "border-width": "1px",
              "text-background-opacity": 1,
              color: isDarkMode ? "#fff" : "#374151",
              "text-background-color": isDarkMode ? "#64748b" : "#fff",
              "text-margin-y": 6,
              "text-background-padding": "2px",
              "text-background-shape": "rectangle",
              "text-border-color": isDarkMode ? "#64748b" : "#9CA3AF",
              "text-border-width": 1,
              "text-border-opacity": 1,
            },
          },
        ]}
        layout={layout}
        className="h-80 w-full"
        elements={graphElements as unknown as any}
      />
      <div className="flex flex-row items-center justify-end space-x-1 lg:space-x-2">
        <button
          onClick={exportGraph}
          className="flex cursor-pointer flex-row items-center space-x-1 rounded-3xl border border-gray-300 px-3 py-1 md:space-x-2 dark:hover:bg-slate-600"
        >
          <FaFileExport className="hidden h-4 w-4 md:flex lg:h-6 lg:w-6 dark:text-white" />
          <span className="text-xs font-medium dark:text-white">Export</span>
        </button>
        <button
          onClick={resetGraph}
          className="flex cursor-pointer flex-row items-center rounded-3xl border border-gray-300 px-3 py-1 md:space-x-2 dark:hover:bg-slate-600"
        >
          <HiArrowPath className="hidden h-4 w-4 md:flex lg:h-6 lg:w-6 dark:text-white" />
          <span className="text-xs font-medium dark:text-white">Snap Back</span>
        </button>
        <button
          onClick={resetZoom}
          className="flex cursor-pointer flex-row items-center space-x-1 rounded-3xl border border-gray-300 px-3 py-1 md:space-x-2 dark:text-white dark:hover:bg-slate-600"
        >
          <HiMagnifyingGlass
            className={`${isDarkMode ? "text-white" : "text-black"} hidden h-4 w-4 md:flex lg:h-6 lg:w-6`}
          />
          <span className="text-xs font-medium">Reset</span>
        </button>
        <div onClick={increaseZoom}>
          <HiMagnifyingGlassPlus className="h-5 w-5 cursor-pointer lg:h-6 lg:w-6 dark:text-white" />
        </div>
        <div onClick={decreaseZoom}>
          <HiMagnifyingGlassMinus className="h-5 w-5 cursor-pointer lg:h-6 lg:w-6 dark:text-white" />
        </div>
      </div>
    </div>
  );
}
