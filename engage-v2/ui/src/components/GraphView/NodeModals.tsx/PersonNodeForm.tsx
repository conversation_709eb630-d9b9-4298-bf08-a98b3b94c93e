import { ChangeEvent, useState } from "react";
import { useEffect } from "react";

import { EditNodesPersonMutationBody } from "../../../client";
import { Node<PERSON>erson } from "../../../model";
import {
  nodeValidationSchemas,
  validateFormData,
} from "../../../utils/validationutils";
import { ActionButtons, ButtonProps } from "../../ActionButtons";
import { Errors } from "../../CreateNode/CreateNodeTypeModal";

interface PersonNodeFormProps {
  nodeData: NodePerson;
  isEditMode: boolean;
  setIsEditMode: (isEditMode: boolean) => void;
  onUpdate: (formData: EditNodesPersonMutationBody) => void;
}

export function PersonNodeForm({
  nodeData,
  isEditMode,
  setIsEditMode,
  onUpdate,
}: PersonNodeFormProps) {
  const [formData, setFormData] = useState({
    company: "",
    email: "",
    first_name: "",
    last_name: "",
    title: "",
  });
  const [errors, setErrors] = useState<Errors>({});

  const handleSubmit = () => {
    // Prepare data for validation - convert empty strings to undefined for optional fields
    const dataToValidate = {
      first_name: formData.first_name,
      last_name: formData.last_name || undefined,
      title: formData.title || undefined,
      company: formData.company || undefined,
      email: formData.email || undefined,
    };

    // Validate form data only when save is clicked
    const { isValid, errors: validationErrors } = validateFormData(
      dataToValidate,
      nodeValidationSchemas.person,
    );

    setErrors(validationErrors);

    if (isValid) {
      onUpdate(formData);
    }
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    // Clear error for this field when user starts typing (optional UX improvement)
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  useEffect(() => {
    if (nodeData) {
      setFormData({
        company: nodeData.company || "",
        email: nodeData.email || "",
        first_name: nodeData.first_name || "",
        last_name: nodeData.last_name || "",
        title: nodeData.title || "",
      });
      // Clear errors when new data is loaded
      setErrors({});
    }
  }, [nodeData]);

  const primaryButton: ButtonProps = {
    label: "Save",
    onClick: handleSubmit,
    variant: "primary",
    // disabled: ,
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      setIsEditMode(false);
    },
    variant: "secondary",
  };

  return (
    <div className="flex w-full flex-col md:w-5/6">
      <div
        className={`${isEditMode ? "flex-col" : "flex-row justify-between"} flex`}
      >
        {isEditMode ? (
          <>
            <label className="font-semibold dark:text-white">First Name:</label>
            <input
              type="text"
              className="mt-1 rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
              value={formData.first_name}
              name="first_name"
              onChange={handleInputChange}
            />
            {errors.first_name && (
              <p className="mt-1 text-red-500">{errors.first_name}</p>
            )}
            <label className="mt-3 font-semibold dark:text-white">
              Last Name:
            </label>
            <input
              type="text"
              className="mt-1 rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
              value={formData.last_name}
              name="last_name"
              onChange={handleInputChange}
            />
            {errors.last_name && (
              <p className="mt-1 text-red-500">{errors.last_name}</p>
            )}
          </>
        ) : (
          <>
            <label className="font-semibold dark:text-slate-300">Name: </label>
            <p className="dark:text-white">
              {nodeData?.first_name} {nodeData?.last_name}
            </p>
          </>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between"} mt-2 flex`}
      >
        <label className="font-semibold dark:text-slate-300">Title: </label>
        {isEditMode ? (
          <>
            <input
              type="text"
              className="mt-1 rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
              value={formData.title}
              name="title"
              onChange={handleInputChange}
            />
            {errors.title && (
              <p className="mt-1 text-red-500">{errors.title}</p>
            )}
          </>
        ) : (
          <p className="dark:text-white">{nodeData?.title}</p>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between"} mt-2 flex`}
      >
        <label className="pr-4 font-semibold dark:text-slate-300">
          Company:
        </label>
        {isEditMode ? (
          <>
            <input
              type="text"
              className="mt-1 rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
              value={formData.company}
              name="company"
              onChange={handleInputChange}
            />
            {errors.company && (
              <p className="mt-1 text-red-500">{errors.company}</p>
            )}
          </>
        ) : (
          <p className="dark:text-white">{nodeData?.company}</p>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between"} mt-2 flex`}
      >
        <label className="font-semibold dark:text-slate-300">Email: </label>
        {isEditMode ? (
          <>
            <input
              type="text"
              className="mt-1 rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
              value={formData.email}
              name="email"
              onChange={handleInputChange}
            />
            {errors.email && (
              <p className="mt-1 text-red-500">{errors.email}</p>
            )}
          </>
        ) : (
          <p className="dark:text-white">{nodeData?.email}</p>
        )}
      </div>
      {isEditMode && (
        <div className="mt-8 flex justify-end">
          <ActionButtons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </div>
      )}
    </div>
  );
}
