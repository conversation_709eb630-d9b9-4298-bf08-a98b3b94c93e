import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns/format";
import { IoIosInformationCircleOutline } from "react-icons/io";

import { Url } from "../model";

type TableRow = Url;

const columnHelper = createColumnHelper<TableRow>();

export const urlColumns = (openEditModal: (node: TableRow) => void) => [
  columnHelper.accessor("url", {
    header: () => "URL",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
  }),
  columnHelper.accessor("client_name", {
    header: () => "Client Name",
    cell: (info) => <div className="py-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("updated_at", {
    header: () => "Last Updated",
    cell: (info) => (
      <div className="py-4">
        {format(new Date(info.getValue() as string), "dd-MMM-yyyy, HH:mm")}
      </div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date"
    }
  }),
  {
    id: "infoColumn",
    header: () => "",
    cell: ({ row }: { row: { original: TableRow } }) => (
      <div className="py-4">
        <IoIosInformationCircleOutline
          className="h-5 w-5"
          onClick={() => openEditModal(row.original)}
        />
      </div>
    ),
  },
];
