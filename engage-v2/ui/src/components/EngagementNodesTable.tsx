import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns";
import { HTMLProps, useEffect, useRef, useState } from "react";
import { GrGraphQl } from "react-icons/gr";
import { HiOutlineCube } from "react-icons/hi";
import { IoIosInformationCircleOutline } from "react-icons/io";

import { Node, NodeGroup } from "../model";
import { getIconType, nodeTypeColors } from "../utils/assets.tsx";
import EditNodeModal from "./GraphView/EditNodeModal";
import Table from "./Table";

type TableRow = NodeGroup | Node;

const columnHelper = createColumnHelper<TableRow>();

const columns = (
  openEditModal: (node: TableRow) => void,
  setSubRowLen: (length: number) => void,
) => [
  columnHelper.accessor((row) => ("name" in row ? row.name : ""), {
    id: "name",
    header: () => {
      return "Node Group";
    },
    maxSize: 0,
    cell: ({ row, getValue }) => {
      //check if it is a node group or a node
      const isNodeGroup = (row: TableRow): row is NodeGroup => {
        return "nodes" in row;
      };
      if (isNodeGroup(row.original)) {
        return (
          <div
            id={`table-${row.original.id}`}
            className={`flex flex-row items-center space-x-2 p-2 ${!row.getCanExpand() && row.getParentRow() ? "ml-4" : ""}`}
          >
            {row.getCanExpand() ? (
              <div className="flex flex-row space-x-2">
                <button
                  {...{
                    onClick: () => {
                      row.getToggleExpandedHandler()();
                      const subRowsLength = row.originalSubRows?.length || 0;
                      setSubRowLen(
                        row.getIsExpanded() ? -subRowsLength : subRowsLength,
                      );
                    },
                    style: { cursor: "pointer" },
                  }}
                >
                  {row.getIsExpanded() ? "-" : "+"}
                </button>
                <HiOutlineCube className="h-6 w-6" />
              </div>
            ) : (
              <HiOutlineCube className="ml-4 h-6 w-6" />
            )}
            <div>{getValue()}</div>
            <a href={`#graph-${row.original.id}`}>
              <GrGraphQl className="h-4 w-4" />
            </a>
          </div>
        );
      } else {
        return (
          <div className="ml-8 flex flex-row">
            <span
              className={`p-2 ${nodeTypeColors.get(row.original.type)}`}
            ></span>
            <div
              className={`flex flex-row items-center space-x-2 p-2 ${!row.getCanExpand() && row.getParentRow() ? "ml-4" : ""}`}
            >
              <IndeterminateCheckbox
                {...{
                  checked: row.getIsSelected(),
                  indeterminate: row.getIsSomeSelected(),
                  onChange: row.getToggleSelectedHandler(),
                }}
              />
              {getIconType(row.original.type)}
              <div className="dark:text-white">{getValue()}</div>
            </div>
          </div>
        );
      }
    },
  }),
  columnHelper.accessor((row) => ("is_active" in row ? row.is_active : false), {
    id: "is_active",
    header: () => "Status",
    cell: (info) => (
      <div className={`${info.getValue() ? "text-green-400" : "text-red-400"}`}>
        {`${info.getValue() === null ? "" : info.getValue() ? "Active" : "Inactive"}`}
      </div>
    ),
  }),
  columnHelper.accessor("created_at", {
    header: () => "Created",
    cell: (info) => (
      <div>{format(new Date(info.getValue()), "dd-MMM-yyyy, HH:mm")}</div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date"
    }
  }),
  columnHelper.accessor("updated_at", {
    header: () => "Last Update",
    cell: (info) => (
      <div>{format(new Date(info.getValue()), "dd-MMM-yyyy, HH:mm")}</div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date"
    }
  }),
  {
    id: "infoColumn",
    header: () => "",
    cell: ({ row }: { row: { original: TableRow } }) => {
      const isNodeGroup = (row: TableRow): row is NodeGroup => {
        return "nodes" in row;
      };
      if (!isNodeGroup(row.original)) {
        return (
          <div>
            <IoIosInformationCircleOutline
              className="h-5 w-5"
              onClick={() => openEditModal(row.original)}
            />
          </div>
        );
      }
    },
  },
];

type Props = {
  nodeGroups: NodeGroup[];
  engagementName: string;
  engagementID: string;
};

function IndeterminateCheckbox({
  indeterminate,
  className = "",
  ...rest
}: { indeterminate?: boolean } & HTMLProps<HTMLInputElement>) {
  const ref = useRef<HTMLInputElement>(null!);

  useEffect(() => {
    if (typeof indeterminate === "boolean") {
      ref.current.indeterminate = !rest.checked && indeterminate;
    }
  }, [ref, indeterminate, rest.checked]);

  return (
    <input
      type="checkbox"
      ref={ref}
      className={className + " cursor-pointer"}
      {...rest}
    />
  );
}

const getSubRows = (row: TableRow): TableRow[] | undefined => {
  if ("nodes" in row) {
    return row.nodes?.map((node) => ({
      ...node,
      name: node.name,
      is_active: null,
    }));
  }
  return undefined;
};

export default function EngagementNodesTable({
  nodeGroups,
  engagementName,
  engagementID,
}: Props) {
  const [isOpenEditModal, setIsOpenEditModal] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<TableRow | null>(null);
  const [subRowLen, setSubRowLen] = useState<number>(0);
  const sortedData = nodeGroups
    .sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
    )
    .map((group) => ({
      ...group,
      nodes: group?.nodes?.sort(
        (nodeA, nodeB) =>
          new Date(nodeB.created_at).getTime() -
          new Date(nodeA.created_at).getTime(),
      ),
    }));

  const openEditModal = (row: TableRow) => {
    setSelectedRow(row);
    setIsOpenEditModal(true);
  };

  const closeEditModal = () => {
    setSelectedRow(null);
    setIsOpenEditModal(false);
  };

  return (
    <>
      <Table
        data={sortedData}
        columns={columns(openEditModal, setSubRowLen)}
        getSubRows={getSubRows}
        subRowLen={subRowLen}
      />
      <EditNodeModal
        isOpen={isOpenEditModal}
        engagementID={engagementID}
        closeModal={closeEditModal}
        nodeDetails={selectedRow}
        isNodeGraph={false}
        engagementName={engagementName}
      />
    </>
  );
}
