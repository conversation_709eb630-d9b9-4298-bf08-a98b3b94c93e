import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { FaAws } from "react-icons/fa";
import { toast } from "react-toastify";

import {
  PostCreateAwsAccountMutationBody,
  getEngagementAwsAccounts,
  postCreateAwsAccount,
} from "../../client.ts";
import { getGetEngagementAwsAccountsQueryKey } from "../../client.ts";
import { AWSAccount } from "../../model/aWSAccount.ts";
import { createRefetchStatus } from "../../utils/refetchEngagementsStatus.ts";
import ErrorMessageModal, { ErrorMessage } from "../ErrorMessageModal.tsx";

type Props = {
  engagementID: string;
  closeModal: () => void;
};

export default function AWSAccountFlow({ engagementID, closeModal }: Props) {
  const queryClient = useQueryClient();
  const [forceRefetch, setForceRefetch] = useState(false);
  const [isOpenErrorModal, setIsOpenErrorModal] = useState(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false);
  const [nickname, setNickname] = useState("");
  const [errorNickname, setErrorNickname] = useState("");

  const awsAccountsQueryKey = getGetEngagementAwsAccountsQueryKey(engagementID);
  const awsAccountsQueryFn = () => getEngagementAwsAccounts(engagementID);

  const {
    data: awsAccounts,
    isLoading,
    isError,
  } = useQuery({
    queryKey: awsAccountsQueryKey,
    queryFn: awsAccountsQueryFn,
    refetchInterval: forceRefetch
      ? 2000 // Poll every 2 seconds for a short period after mutation
      : createRefetchStatus<AWSAccount>("accounts", {
          refetchIntervalSeconds: 5000,
          statusField: "account_cloud_status",
        }),
  });

  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [inputError, setInputError] = useState("");

  // React Query: Mutation for creating AWS account
  const awsAccountMutation = useMutation({
    mutationFn: (formData: PostCreateAwsAccountMutationBody) =>
      postCreateAwsAccount(engagementID, { nickname: formData.nickname }),
    onError: (error: any) => {
      let message = "An unknown error occurred.";
      if (error?.response?.data?.detail) {
        message = error.response.data.detail;
      }
      if (
        message.includes("already exists for this engagement") &&
        message.includes("Nickname")
      ) {
        setInputError(message);
        setErrorNickname(nickname);
      } else {
        setErrorMessage({
          title: "Error creating AWS account",
          message,
        });
        setIsOpenErrorModal(true);
      }
    },
    onSettled: () => {
      setForceRefetch(true);
      setTimeout(() => setForceRefetch(false), 5000); // 5 seconds of forced polling
      queryClient.invalidateQueries({ queryKey: awsAccountsQueryKey });
      queryClient.invalidateQueries({
        queryKey: getGetEngagementAwsAccountsQueryKey(engagementID),
      });
      setHasSubmitted(true);
      setNickname("");
      toast.success(
        "Request for creating AWS account is successfully submitted.",
      );
      setIsConfirmModalOpen(false);
    },
  });

  const handleCreateAccount = () => {
    setIsConfirmModalOpen(true);
    setNickname("");
    setInputError("");
  };

  const handleConfirmCreate = () => {
    if (nickname && !inputError) {
      awsAccountMutation.mutate({ nickname });
    }
  };

  return (
    <div className="space-y-6">
      {/* AWS Accounts Table */}
      <div className="mb-6">
        <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-white">
          <FaAws className="mr-2 inline-block h-5 w-5 text-orange-500" />
          Existing AWS Accounts
        </h3>

        {isLoading ? (
          <div className="flex h-24 items-center justify-center">
            <div className="flex animate-pulse space-x-2">
              <div className="h-2 w-2 rounded-full bg-purple-600"></div>
              <div className="h-2 w-2 rounded-full bg-purple-600"></div>
              <div className="h-2 w-2 rounded-full bg-purple-600"></div>
            </div>
          </div>
        ) : isError ? (
          <div className="rounded-md border border-red-200 bg-red-50 p-4 text-red-600 dark:border-red-800 dark:bg-red-900/20 dark:text-red-400">
            Failed to load AWS accounts. Please try again.
          </div>
        ) : (
          <div className="overflow-hidden rounded-lg border border-gray-200 shadow dark:border-gray-700">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-800">
                  <tr>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                    >
                      Account ID
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                    >
                      Nickname
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                    >
                      AWS Account ID
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                    >
                      Current Status In AWS
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                    >
                      Initial Creation Status
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                    >
                      Created At
                    </th>
                    <th
                      scope="col"
                      className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                    >
                      Created By
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
                  {awsAccounts?.accounts && awsAccounts.accounts.length > 0 ? (
                    awsAccounts.accounts.map((account) => (
                      <tr
                        key={account.account_name}
                        className="transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/60"
                      >
                        <td className="px-4 py-3 text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">
                          {account.account_name}
                        </td>
                        <td className="px-4 py-3 text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">
                          {account.nickname}
                        </td>
                        <td className="px-4 py-3 font-mono text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">
                          {account.account_cloud_id || "-"}
                        </td>
                        <td className="px-4 py-3 text-sm whitespace-nowrap">
                          <span
                            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                              account.account_cloud_status === "ACTIVE"
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                : "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                            }`}
                          >
                            {account.account_cloud_status || "PENDING"}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm whitespace-nowrap">
                          <span
                            className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                              account.account_creation_status === "SUCCESS"
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                : account.account_creation_status === "FAILED"
                                  ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                                  : "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                            }`}
                          >
                            {account.account_creation_status}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                          {new Date(account.created_at).toLocaleString()}
                        </td>
                        <td className="px-4 py-3 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                          {account.created_by}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td
                        colSpan={6}
                        className="px-4 py-6 text-center text-sm text-gray-500 dark:text-gray-400"
                      >
                        No AWS accounts found. Create your first account below.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        )}
      </div>

      <hr className="border-gray-200 dark:border-gray-700" />

      {/* Create Account Section */}
      <div className="rounded-lg bg-gray-50 p-5 shadow-sm dark:bg-gray-800/50">
        <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-white">
          <FaAws className="mr-2 inline-block h-5 w-5 text-orange-500" />
          Create New AWS Account
        </h3>

        <p className="mb-4 text-sm text-gray-600 dark:text-gray-300">
          A new AWS account will be provisioned under this engagement. This
          action will create cloud resources and may incur costs.
        </p>

        {hasSubmitted && (
          <div className="mb-4 rounded-md border border-green-200 bg-green-50 p-3 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400">
            Account creation request sent. You can track its status in the table
            above.
          </div>
        )}

        <div className="flex justify-end gap-4 pt-2">
          <button
            className="rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
            onClick={closeModal}
          >
            {hasSubmitted ? "Close" : "Cancel"}
          </button>
          <button
            className="rounded-md bg-purple-600 px-4 py-2 text-white transition-colors hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
            onClick={handleCreateAccount}
            disabled={hasSubmitted || awsAccountMutation.isPending}
          >
            {awsAccountMutation.isPending ? (
              <span className="flex items-center">
                <svg
                  className="mr-2 -ml-1 h-4 w-4 animate-spin text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </span>
            ) : hasSubmitted ? (
              "Submitted"
            ) : (
              "Create AWS Account"
            )}
          </button>
        </div>
      </div>

      {/* Error Modal */}
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={() => setIsOpenErrorModal(false)}
        errorMessage={errorMessage}
        primaryButton={{
          label: "Close",
          onClick: () => setIsOpenErrorModal(false),
          variant: "primary",
        }}
      />

      {/* Account Creation Confirmation Modal */}
      <div
        className="fixed inset-0 z-50 overflow-y-auto"
        style={{ display: isConfirmModalOpen ? "block" : "none" }}
      >
        <div className="flex min-h-screen items-center justify-center">
          <div className="relative z-10 mx-auto w-full max-w-lg rounded-lg bg-white p-6 shadow-xl dark:bg-gray-800">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                <FaAws className="mr-2 inline-block h-5 w-5 text-orange-500" />
                Create New AWS Account
              </h3>
              <button
                onClick={() => setIsConfirmModalOpen(false)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-300 dark:hover:text-white"
              >
                ✕
              </button>
            </div>

            <div className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-300">
                This action will provision cloud resources and may incur costs.
                Please provide a name for the new account:
              </p>

              <div className="mt-2">
                <label
                  htmlFor="account-nickname"
                  className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300"
                >
                  Account Name <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  id="account-nickname"
                  value={nickname}
                  onChange={(e) => {
                    const value = e.target.value;
                    setNickname(value);
                    if (inputError && value !== errorNickname) {
                      setInputError("");
                      setErrorNickname("");
                    }
                  }}
                  className={`w-full rounded-md px-3 py-2 shadow-sm focus:border-purple-500 focus:ring-purple-500 dark:bg-gray-700 dark:text-white ${
                    inputError
                      ? "border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  placeholder="e.g., Production, Development, Testing"
                  autoFocus
                />
                {inputError && (
                  <p className="mt-2 text-sm text-red-600 dark:text-red-400">
                    {inputError}
                  </p>
                )}
              </div>

              <div className="mt-6 flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setIsConfirmModalOpen(false)}
                  className="rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 transition-colors hover:bg-gray-50 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleConfirmCreate}
                  disabled={!nickname || !!inputError}
                  className="rounded-md bg-purple-600 px-4 py-2 text-white transition-colors hover:bg-purple-700 focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50"
                >
                  Create
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
