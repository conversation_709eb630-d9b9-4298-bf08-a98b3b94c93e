import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { VscAzure } from "react-icons/vsc";
import { toast } from "react-toastify";

import {
  getGetEngagementAzureTenantsQuery<PERSON>ey,
  useGetEngagementAzureTenants,
  usePostCreateAzureTenant,
} from "../../client";
import { getAzureStatusIcon } from "../../utils/assets.tsx";
import { createRefetchStatus } from "../../utils/refetchEngagementsStatus.ts";
import { ErrorMessage } from "../ErrorMessageModal.tsx";

interface AzureTenantForm {
  azure_tenant_id: string;
  azure_subscription_id: string;
  azure_app_id: string;
  azure_app_secret: string;
}

interface Props {
  engagementID: string;
  closeModal: () => void;
}

export default function AzureTenantFlow({ engagementID, closeModal }: Props) {
  // Single tenant state (no array)
  const [tenant, setTenant] = useState<AzureTenantForm>({
    azure_tenant_id: "",
    azure_subscription_id: "",
    azure_app_id: "",
    azure_app_secret: "",
  });

  const [errors, setErrors] = useState<
    Partial<Record<keyof AzureTenantForm, string>>
  >({});
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
  const [isOpenErrorModal, setIsOpenErrorModal] = useState(false);
  const [hasSubmitted, setHasSubmitted] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [forceRefetch, setForceRefetch] = useState(false);

  const queryClient = useQueryClient();

  const {
    data: azureTenants,
    isLoading: tenantsLoading,
    isError: tenantsError,
  } = useGetEngagementAzureTenants(engagementID, {
    query: {
      refetchInterval: forceRefetch
        ? 1000 // More frequent polling during status check
        : createRefetchStatus("tenants", {
            refetchIntervalSeconds: 5000,
            statusField: "account_cloud_status", // Fixed field name
          }),
    },
  });

  const handleMutationError = (error: any) => {
    const detailRaw = error?.response?.data?.detail;
    console.log("handleMutationError called with detail:", detailRaw);

    if (typeof detailRaw !== "string") {
      console.log("detail is not a string");
      setErrorMessage({
        title: "Unexpected Error",
        message: "An unknown error occurred while processing your request.",
      });
      setIsOpenErrorModal(true);
      return;
    }

    const prefix = "Validation failed: ";
    let detail = detailRaw;

    if (detailRaw.startsWith(prefix)) {
      detail = detailRaw.substring(prefix.length).trim();
      console.log("Stripped prefix, detail now:", detail);
    }

    const userInputErrorMap: Partial<Record<keyof AzureTenantForm, string>> =
      {};
    const globalErrors: string[] = [];

    // inputRegex for input errors
    const inputRegex =
      /\{([\w-]+)(?:\s+([\w-]+))?\s+(Tenant|Subscription)\s+(.+?)\}/g;
    let match: RegExpExecArray | null;

    while ((match = inputRegex.exec(detail)) !== null) {
      console.log("Matched input error:", match);
      const [_, tenantID, subscriptionID, type, message] = match;

      if (type === "Tenant" && tenant.azure_tenant_id === tenantID) {
        userInputErrorMap.azure_tenant_id = message;
      }
      if (
        type === "Subscription" &&
        tenant.azure_subscription_id === subscriptionID
      ) {
        userInputErrorMap.azure_subscription_id = message;
      }
    }

    // secretRegex for secret errors
    const secretRegex = /\{([\w-]+)\s+([\w-]+)\s+(.+?)\}/g;
    while ((match = secretRegex.exec(detail)) !== null) {
      console.log("Matched secret/global error:", match);
      const [_, tenantID, subscriptionID, message] = match;

      if (message.includes("Failed to save secrets")) {
        globalErrors.push(
          `Tenant ID: ${tenantID}, Subscription ID: ${subscriptionID}: ${message}`,
        );
      }
    }

    console.log("userInputErrorMap:", userInputErrorMap);
    console.log("globalErrors:", globalErrors);

    setErrors(userInputErrorMap);

    if (globalErrors.length > 0) {
      console.log("Setting error modal open with global errors");
      setErrorMessage({
        title: "Azure Tenant Secret Creation Failed",
        message: globalErrors.join("\n"),
      });
      setIsOpenErrorModal(true);
    } else if (Object.keys(userInputErrorMap).length === 0) {
      console.log("Setting error modal open with generic validation message");
      setErrorMessage({
        title: "Validation Failed",
        message: detailRaw,
      });
      setIsOpenErrorModal(true);
    }
  };

  const mutation = usePostCreateAzureTenant({
    mutation: {
      onError: handleMutationError,
      onSuccess: () => {
        setForceRefetch(true);
        setTimeout(() => setForceRefetch(false), 15000); // Extended to 15 seconds for status check
        toast.success(
          "Tenant added successfully - checking subscription status...",
        );
        queryClient.invalidateQueries({
          queryKey: getGetEngagementAzureTenantsQueryKey(engagementID),
        });
        setHasSubmitted(true);
        setHasChanges(false);
        setTenant({
          azure_tenant_id: "",
          azure_subscription_id: "",
          azure_app_id: "",
          azure_app_secret: "",
        });
        setErrors({});
      },
    },
  });

  const handleChange = (field: keyof AzureTenantForm, value: string) => {
    setHasChanges(true);
    setHasSubmitted(false);

    setTenant((prev) => ({
      ...prev,
      [field]: value,
    }));

    setErrors((prevErrors) => {
      const { [field]: _, ...rest } = prevErrors;
      return rest;
    });
  };

  const isFormValid = () => {
    return (
      tenant.azure_tenant_id.trim() !== "" && Object.keys(errors).length === 0
    );
  };

  const validateTenant = () => {
    const formErrors: Partial<Record<keyof AzureTenantForm, string>> = {};

    const hasSubscription = tenant.azure_subscription_id.trim() !== "";
    const hasAppId = tenant.azure_app_id.trim() !== "";
    const hasAppSecret = tenant.azure_app_secret.trim() !== "";

    if (hasSubscription && (!hasAppId || !hasAppSecret)) {
      if (!hasAppId)
        formErrors.azure_app_id =
          "App ID is required when subscription is provided.";
      if (!hasAppSecret)
        formErrors.azure_app_secret =
          "App Secret is required when subscription is provided.";
    }

    if (!hasSubscription && (hasAppId || hasAppSecret)) {
      if (hasAppId)
        formErrors.azure_app_id =
          "Remove App ID when subscription is not provided.";
      if (hasAppSecret)
        formErrors.azure_app_secret =
          "Remove App Secret when subscription is not provided.";
    }

    setErrors(formErrors);
    return Object.keys(formErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateTenant()) {
      toast.error("Please fix validation errors before submitting.");
      return;
    }

    const tenantData = {
      engagementId: engagementID,
      data: {
        azure_tenant_id: tenant.azure_tenant_id.trim(),
        azure_subscription_id: tenant.azure_subscription_id.trim(),
        azure_app_id: tenant.azure_app_id.trim(),
        azure_app_secret: tenant.azure_app_secret.trim(),
      },
    };

    mutation.mutate(tenantData);
  };

  return (
    <>
      <div className="max-h-[80vh] space-y-4 overflow-y-auto pr-1">
        {/* Azure Tenants Table */}
        <div className="mb-6">
          <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-white">
            <VscAzure className="mr-2 inline-block h-5 w-5 text-blue-500" />
            Existing Azure Tenants
          </h3>

          {tenantsLoading ? (
            <div className="flex h-24 items-center justify-center">
              <div className="flex animate-pulse space-x-2">
                <div className="h-2 w-2 rounded-full bg-purple-600"></div>
                <div className="h-2 w-2 rounded-full bg-purple-600"></div>
                <div className="h-2 w-2 rounded-full bg-purple-600"></div>
              </div>
            </div>
          ) : tenantsError ? (
            <div className="rounded-md border border-red-200 bg-red-50 p-4 text-red-600 dark:border-red-800 dark:bg-red-900/20 dark:text-red-400">
              Failed to load Azure tenants. Please try again.
            </div>
          ) : (
            <>
              {(azureTenants?.tenants?.length ?? 0) > 0 ? (
                <>
                  <div className="overflow-hidden rounded-lg border border-gray-200 shadow dark:border-gray-700">
                    <input
                      type="text"
                      placeholder="Search by Tenant ID or Subscription ID"
                      className="mb-0 w-full rounded-md border border-b p-2"
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <div className="overflow-x-auto">
                      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead className="bg-gray-50 dark:bg-gray-800">
                          <tr>
                            <th
                              scope="col"
                              className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                            >
                              Tenant ID
                            </th>
                            <th
                              scope="col"
                              className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                            >
                              Subscription ID
                            </th>
                            <th
                              scope="col"
                              className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                            >
                              Secrets Saved
                            </th>
                            <th
                              scope="col"
                              className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                            >
                              Adding Status
                            </th>
                            <th
                              scope="col"
                              className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                            >
                              Subscription Cloud Status
                            </th>
                            <th
                              scope="col"
                              className="px-4 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300"
                            >
                              Added At
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-800 dark:bg-gray-900">
                          {(() => {
                            const filtered = (
                              azureTenants?.tenants ?? []
                            ).filter(
                              (t) =>
                                t.tenant_id
                                  .toLowerCase()
                                  .includes(searchTerm.toLowerCase()) ||
                                t.subscription_id
                                  ?.toLowerCase()
                                  .includes(searchTerm.toLowerCase()),
                            );

                            return filtered.length > 0 ? (
                              filtered.map((tenant) => (
                                <tr
                                  key={`${tenant.tenant_id}-${tenant.subscription_id}`}
                                  className="transition-colors hover:bg-gray-50 dark:hover:bg-gray-800/60"
                                >
                                  <td className="px-4 py-3 font-mono text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">
                                    {tenant.tenant_id}
                                  </td>
                                  <td className="px-4 py-3 font-mono text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">
                                    {tenant.subscription_id || "-"}
                                  </td>
                                  <td className="px-4 py-3 font-mono text-sm whitespace-nowrap text-gray-900 dark:text-gray-100">
                                    {String(tenant.secrets_saved)}
                                  </td>
                                  <td className="px-4 py-3 text-sm whitespace-nowrap">
                                    <span
                                      className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
                                        tenant.creation_status === "SUCCESS"
                                          ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                          : tenant.creation_status === "FAILED"
                                            ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                                            : "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                                      }`}
                                    >
                                      {tenant.creation_status}
                                    </span>
                                  </td>
                                  <td className="px-4 py-3 text-sm whitespace-nowrap">
                                    <div className="flex items-center space-x-2">
                                      {tenant.account_cloud_status ? (
                                        <>
                                          {getAzureStatusIcon(
                                            tenant.account_cloud_status,
                                          )}
                                          <span className="text-sm text-gray-900 dark:text-gray-100">
                                            {tenant.account_cloud_status}
                                          </span>
                                        </>
                                      ) : (
                                        <span className="text-sm text-gray-500 dark:text-gray-400">
                                          -
                                        </span>
                                      )}
                                    </div>
                                  </td>
                                  <td className="px-4 py-3 text-sm whitespace-nowrap text-gray-500 dark:text-gray-400">
                                    {new Date(
                                      tenant.created_at,
                                    ).toLocaleString()}
                                  </td>
                                </tr>
                              ))
                            ) : (
                              <tr>
                                <td
                                  colSpan={6}
                                  className="px-4 py-6 text-center text-sm text-gray-500 dark:text-gray-400"
                                >
                                  No matching tenants found.
                                </td>
                              </tr>
                            );
                          })()}
                        </tbody>
                      </table>
                    </div>
                  </div>
                </>
              ) : (
                <div className="rounded-lg border border-gray-200 bg-gray-50 p-6 text-center text-gray-500 dark:border-gray-700 dark:bg-gray-800 dark:text-gray-400">
                  No tenants found for this engagement. Add your first tenant
                  below.
                </div>
              )}
            </>
          )}
        </div>

        <hr className="border-gray-200 dark:border-gray-700" />

        {/* Tenant Input Form */}
        <div className="rounded-lg bg-gray-50 p-5 shadow-sm dark:bg-gray-800/50">
          <h3 className="mb-3 text-lg font-semibold text-gray-900 dark:text-white">
            <VscAzure className="mr-2 inline-block h-5 w-5 text-blue-500" />
            Add Azure Tenant
          </h3>

          <p className="mb-4 text-sm text-gray-600 dark:text-gray-300">
            Add an Azure tenant to this engagement. You'll need the Tenant ID
            and optionally Subscription ID with credentials.
          </p>

          {hasSubmitted && (
            <div className="mb-4 rounded-md border border-green-200 bg-green-50 p-3 text-green-700 dark:border-green-800 dark:bg-green-900/20 dark:text-green-400">
              Tenant submitted. You can track the creation status in the table
              above.
            </div>
          )}

          <div className="mb-4 space-y-2 rounded-md border bg-white p-4 dark:border-gray-700 dark:bg-gray-800">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <label className="block">
                Tenant ID <span className="text-red-500">*</span>
                <input
                  className="mt-1 w-full rounded-md border p-2 dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                  value={tenant.azure_tenant_id}
                  onChange={(e) =>
                    handleChange("azure_tenant_id", e.target.value)
                  }
                  placeholder="Azure Tenant ID"
                  required
                />
                {errors.azure_tenant_id && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.azure_tenant_id}
                  </p>
                )}
              </label>

              <label className="block">
                Subscription ID
                <input
                  className="mt-1 w-full rounded-md border p-2"
                  value={tenant.azure_subscription_id}
                  onChange={(e) =>
                    handleChange("azure_subscription_id", e.target.value)
                  }
                />
                {errors.azure_subscription_id && (
                  <p className="mt-1 text-sm text-red-500">
                    {errors.azure_subscription_id}
                  </p>
                )}
              </label>

              <label className="block">
                App ID
                <input
                  className="mt-1 w-full rounded-md border p-2"
                  value={tenant.azure_app_id}
                  onChange={(e) => handleChange("azure_app_id", e.target.value)}
                />
                {errors.azure_app_id && (
                  <div className="text-sm text-red-500">
                    {errors.azure_app_id}
                  </div>
                )}
              </label>

              <label className="block">
                App Secret
                <input
                  type="password"
                  className="mt-1 w-full rounded-md border p-2"
                  value={tenant.azure_app_secret}
                  onChange={(e) =>
                    handleChange("azure_app_secret", e.target.value)
                  }
                />
                {errors.azure_app_secret && (
                  <div className="text-sm text-red-500">
                    {errors.azure_app_secret}
                  </div>
                )}
              </label>
            </div>
          </div>

          <div className="mt-6 flex justify-end border-t pt-4">
            <div className="flex gap-4">
              <button
                type="button"
                onClick={closeModal}
                className="w-full rounded-md border border-solid border-slate-300 px-6 py-3 hover:bg-purple-700 hover:text-white md:w-max dark:bg-transparent"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleSave}
                disabled={!isFormValid() || !hasChanges}
                className={`rounded-md px-6 py-3 text-white ${
                  !isFormValid() || !hasChanges
                    ? "cursor-not-allowed bg-gray-400"
                    : "bg-purple-700 hover:bg-purple-800"
                }`}
              >
                {hasSubmitted ? "Saved" : "Save"}
              </button>
            </div>
          </div>
        </div>
      </div>
      {isOpenErrorModal && errorMessage && (
        <div className="pointer-events-none fixed inset-0 z-50 flex items-center justify-center">
          <div className="pointer-events-auto w-full max-w-md rounded-lg bg-white p-6 shadow-lg dark:bg-gray-800">
            <h3 className="mb-4 text-xl font-semibold text-black dark:text-white">
              {errorMessage.title || "Error"}
            </h3>
            <p className="mb-6 text-gray-700 dark:text-gray-300">
              {errorMessage.message}
            </p>
            <div className="flex justify-end">
              <button
                onClick={() => setIsOpenErrorModal(false)}
                className="rounded bg-purple-700 px-4 py-2 text-white hover:bg-purple-800"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
