import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns/format";
import { IoIosInformationCircleOutline } from "react-icons/io";

import { CloudHost } from "../model";

type TableRow = CloudHost;

const columnHelper = createColumnHelper<TableRow>();

export const hosts_columns = (openEditModal: (node: TableRow) => void) => [
  columnHelper.accessor("name", {
    header: () => "Name",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
  }),
  columnHelper.accessor("alternative_names", {
    header: () => "Alternative names",
    cell: (info) => {
      const names = info.getValue() as string[];
      const joinedNames = names.join(", ");
      return <div className="py-4">{joinedNames}</div>;
    },
  }),
  columnHelper.accessor("ip_addresses", {
    header: () => "IP Addresses",
    cell: (info) => {
      const addresses = info.getValue() as string[];
      return addresses ? <div>{addresses.join(", ")}</div> : null;
    },
  }),
  columnHelper.accessor("client_name", {
    header: () => "Client Name",
    cell: (info) => <div className="py-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("updated_at", {
    header: () => "Last Updated",
    cell: (info) => (
      <div className="py-4">
        {format(new Date(info.getValue() as string), "dd-MMM-yyyy, HH:mm")}
      </div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  {
    id: "infoColumn",
    header: () => "",
    cell: ({ row }: { row: { original: TableRow } }) => (
      <div className="py-4">
        <IoIosInformationCircleOutline
          className="h-5 w-5"
          onClick={() => openEditModal(row.original)}
        />
      </div>
    ),
  },
];
