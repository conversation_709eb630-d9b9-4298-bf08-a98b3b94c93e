import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns/format";
import { IoIosInformationCircleOutline } from "react-icons/io";

import { Node, NodeEngagementGroup } from "../model";

export type TableRow = NodeEngagementGroup | Node;

const nodeEngagementGroupColumnHelper = createColumnHelper<NodeEngagementGroup>();

export const columns = (openEditModal: (node: NodeEngagementGroup) => void) => [
  nodeEngagementGroupColumnHelper.accessor("engagement_name", {
    id: "engagement_name",
    header: () => "Engagement Title",
    cell: (info) => (
      <div className="flex flex-row items-center space-x-2 p-2">
        {info?.getValue() as string}
      </div>
    ),
  }),
  nodeEngagementGroupColumnHelper.accessor("type", {
    header: () => "Node Type",
    cell: (info) => {
      const value = info.getValue() as string;
      return (
        <div className="flex flex-row items-center py-4">
          {value
            .toLowerCase()
            .replace(/_/g, " ")
            .replace(/\b\w/g, (char: string) => char.toUpperCase())}
        </div>
      );
    },
  }),
  nodeEngagementGroupColumnHelper.accessor("name", {
    header: () => "nodeGroup_name",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
  }),
  nodeEngagementGroupColumnHelper.accessor("client_name", {
    header: () => "Client Name",
    cell: (info) => <div className="py-4">{info.getValue() as string}</div>,
  }),
  nodeEngagementGroupColumnHelper.accessor("updated_at", {
    header: () => "Last Updated",
    cell: (info) => (
      <div>{format(new Date(info.getValue()), "dd-MMM-yyyy, HH:mm")}</div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date"
    }
  }),
  {
    id: "infoColumn",
    header: () => "",
    cell: ({ row }: { row: { original: NodeEngagementGroup } }) => (
      <div className="py-4">
        <IoIosInformationCircleOutline
          className="h-5 w-5"
          onClick={() => openEditModal(row.original)}
        />
      </div>
    ),
  },
];
