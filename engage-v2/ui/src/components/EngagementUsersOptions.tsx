import { MdO<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MdOutlinePersonSearch } from "react-icons/md";

import { EngagementUser } from "../model";

export const EngagementUsersOptions = (user: EngagementUser) => {
  if (!user) return null;

  const hasBothMissing = !user.valid_custom_username && !user.valid_ssh_key;

  return (
    <div className="flex items-center space-x-2 pl-4">
      {hasBothMissing ? (
        <div className="flex items-center space-x-1">
          <MdOutlinePersonSearch className="h-5 w-5 text-rose-600 dark:text-red-800" />
          <span className="text-red-500">Invalid Username</span>
          <MdOutlineLockPerson className="h-5 w-5 text-rose-600 dark:text-red-800" />
          <span className="text-red-500">Invalid SSH Key</span>
        </div>
      ) : (
        <>
          {!user.valid_custom_username ? (
            <div className="flex items-center space-x-1">
              <MdOutlinePersonSearch className="h-5 w-5 text-rose-600 dark:text-red-800" />
              <span className="text-red-500">Invalid Username</span>
            </div>
          ) : null}
          {!user.valid_ssh_key ? (
            <div className="flex items-center space-x-1">
              <MdOutlineLockPerson className="h-5 w-5 text-rose-600 dark:text-red-800" />
              <span className="text-red-500">Invalid SSH Key</span>
            </div>
          ) : null}
        </>
      )}
      <span>
        {`${user.full_name}`}
        {user.app_role === "Admin" && (
          <span className="ml-2">(Admin User)</span>
        )}
      </span>
    </div>
  );
};
