import { Link } from "@tanstack/react-router";
import { useLocation } from "@tanstack/react-router";
import { useEffect, useRef, useState } from "react";

import { capitalizeFirstLetterEachWord } from "../utils/assets";

function useBreadCrumbs() {
  const current = useLocation();

  const route_history = current.pathname
    .split("/")
    .filter((x) => x && x.length > 0);
  const breadcrumb_routes = route_history.map((pathParameter, index) => {
    const isLast = index === route_history.length - 1;
    const route = `/${route_history.slice(0, index + 1).join("/")}`;
    const isClickable =
      !route.includes("users") && !route.includes("security") && !isLast;
    if (pathParameter === "engagements" && !isLast) {
      return {
        name: "Engagements",
        path: "/",
        isClickable: true,
      };
    }

    return {
      name: pathParameter,
      path: route,
      isClickable,
    };
  });

  return { breadcrumb_routes };
}

export default function BreadCrumbs() {
  const { breadcrumb_routes } = useBreadCrumbs();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLUListElement | null>(null);
  const lastBreadcrumb = breadcrumb_routes[breadcrumb_routes.length - 1];
  const hiddenBreadcrumbs = breadcrumb_routes.slice(0, -1);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <div>
      <ul className="sticky top-0 flex items-center">
        <li className="inline-flex items-center">
          <Link to="/">
            <span className="text-gray-600 hover:text-purple-500 dark:text-white">
              Home
            </span>
          </Link>
        </li>
        {hiddenBreadcrumbs.length > 0 && (
          <li className="inline-flex items-center sm:hidden">
            <span className="mx-4 text-gray-600 dark:text-white">/</span>
            <button
              onClick={() => setIsDropdownOpen((prev) => !prev)}
              className="text-gray-600 hover:text-purple-500 dark:text-white"
            >
              ...
            </button>
            {isDropdownOpen && (
              <ul
                ref={dropdownRef}
                className="absolute mt-20 rounded-md border bg-white py-1 shadow-lg dark:border-gray-700 dark:bg-gray-800"
              >
                {hiddenBreadcrumbs.map((route, index) => (
                  <li key={index} className="px-5 py-2">
                    {route.isClickable ? (
                      <Link
                        to={route.path}
                        className="block text-gray-600 hover:bg-gray-100 dark:text-gray-200 dark:hover:bg-gray-700"
                      >
                        {capitalizeFirstLetterEachWord(route.name)}
                      </Link>
                    ) : (
                      <span className="block text-gray-400 dark:text-gray-500">
                        {capitalizeFirstLetterEachWord(route.name)}
                      </span>
                    )}
                  </li>
                ))}
              </ul>
            )}
          </li>
        )}
        {hiddenBreadcrumbs.length > 0 &&
          hiddenBreadcrumbs.map((route, index) => (
            <li
              key={index}
              className="hidden items-center text-gray-600 hover:text-purple-500 sm:inline-flex dark:text-gray-400"
            >
              <span className="mx-4">/</span>
              {route.isClickable ? (
                <Link to={route.path}>
                  {capitalizeFirstLetterEachWord(route.name)}
                </Link>
              ) : (
                <span className="text-gray-400 dark:text-gray-500">
                  {capitalizeFirstLetterEachWord(route.name)}
                </span>
              )}
            </li>
          ))}
        {lastBreadcrumb && (
          <li className="inline-flex items-center">
            <span className="mx-4 text-gray-600 dark:text-white">/</span>
            <span className="font-medium text-purple-600 dark:text-purple-300">
              {capitalizeFirstLetterEachWord(lastBreadcrumb.name)}
            </span>
          </li>
        )}
      </ul>
    </div>
  );
}
