import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns/format";

import { UserLogsAssignment } from "../../model";
import {
  capitalizeFirstLetterEachWord,
  getStatusColor,
  getStatusIcon,
} from "../../utils/assets";

export type TableRow = UserLogsAssignment;

const columnHelper = createColumnHelper<TableRow>();

export const LogsColumn: ColumnDef<TableRow, string>[] = [
  columnHelper.accessor("id", {
    header: () => "ID",
    cell: (info) => (
      <div className="flex flex-row items-center space-x-2 p-2">
        {info?.getValue() as string}
      </div>
    ),
  }),
  columnHelper.accessor("node_id", {
    header: () => "Node ID",
    cell: (info) => (
      <div className="flex flex-row items-center space-x-2 p-2">
        {info?.getValue() as string}
      </div>
    ),
  }),
  columnHelper.accessor("name", {
    header: () => "Name",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
  }),
  columnHelper.accessor("type", {
    header: () => "Type",
    cell: (info) => (
      <div className="py-4">
        {capitalizeFirstLetterEachWord(info.getValue())}
      </div>
    ),
  }),
  columnHelper.accessor("user_id", {
    header: () => "User ID",
    cell: (info) => <div className="py-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("user_custom_username_used", {
    header: () => "Username",
    cell: (info) => <div className="py-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("status", {
    header: () => "Status",
    cell: (info) => (
      <div className="flex py-4">
        <span
          className={`flex flex-row content-center items-center rounded-full px-2 py-1 shadow-md ${getStatusColor(info.getValue())}`}
        >
          {getStatusIcon(info.getValue())}
          {info
            .getValue()
            .toLowerCase()
            .replace(/[-_]/g, " ")
            .replace(/\b\w/g, (char: string) => char.toUpperCase())}
        </span>
      </div>
    ),
  }),
  columnHelper.accessor("message", {
    header: () => "Message",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
  }),
  columnHelper.accessor("created_at", {
    header: () => "Created At",
    cell: (info) => (
      <div>{format(new Date(info.getValue()), "dd-MMM-yyyy, HH:mm")}</div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
];
