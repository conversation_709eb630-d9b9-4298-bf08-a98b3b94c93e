import { useQueryClient } from "@tanstack/react-query";
import { useRef, useState } from "react";
import {
  HiOutlineDocumentArrowDown,
  HiOutlineDocumentArrowUp,
} from "react-icons/hi2";
import { toast } from "react-toastify";

import {
  getGetInventoryDomainsQuery<PERSON>ey,
  getGetInventoryQueryKey,
  getGetInventoryUrlsQueryKey,
  getInventoryDomainsTemplate,
  usePostInventoryDomainsImportBase64,
} from "../client";

// Function to convert file to base64
const convertFileToBase64 = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => {
      if (typeof reader.result === "string") {
        // Remove the data URL prefix (e.g., "data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,")
        const base64String = reader.result.split(",")[1];
        resolve(base64String);
      } else {
        reject(new Error("Failed to convert file to base64"));
      }
    };
    reader.onerror = (error) => reject(error);
  });
};

export const ExcelImporter = () => {
  const [, setIsUploading] = useState(false);
  const [isTemplateDownloading, setIsTemplateDownloading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();

  // Use the generated mutation for importing domains
  const importDomainsMutation = usePostInventoryDomainsImportBase64({
    mutation: {
      onSuccess: (data) => {
        toast.success(
          `Successfully imported ${data.importedCount || 0} domains`,
        );

        // Invalidate relevant queries to refresh the data
        queryClient.invalidateQueries({
          queryKey: getGetInventoryUrlsQueryKey(),
        });

        // Also invalidate the main inventory query to update counts
        queryClient.invalidateQueries({
          queryKey: getGetInventoryQueryKey(),
        });

        queryClient.invalidateQueries({
          queryKey: getGetInventoryDomainsQueryKey(),
        });
      },
      onError: (error: any) => {
        const errorMessage =
          error.message || "Failed to import domains. Please try again.";
        toast.error(errorMessage);

        // If it's a column error, suggest downloading the template
        if (errorMessage.includes("column") || errorMessage.includes("CSV")) {
          toast.info(
            "Try downloading and using our template file for the correct format",
            {
              autoClose: 8000,
            },
          );
        }
      },
    },
  });

  // Template download is handled directly via API call in downloadTemplate function

  const downloadTemplate = async () => {
    setIsTemplateDownloading(true);
    try {
      const response = await getInventoryDomainsTemplate({
        responseType: "text",
      });

      // Create a blob from the response data
      const blob = new Blob([response], { type: "text/csv" });
      const url = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.href = url;
      a.download = "domain_import_template.csv";
      document.body.appendChild(a);
      a.click();

      setTimeout(() => {
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }, 100);

      toast.success("Template downloaded successfully");
    } catch (error: any) {
      toast.error(
        error?.response?.data?.error ||
          error.message ||
          "Failed to download template. Please try again.",
      );
    } finally {
      setIsTemplateDownloading(false);
    }
  };

  const handleFileChange = async (
    event: React.ChangeEvent<HTMLInputElement>,
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Check file extension
    const fileExt = file.name.split(".").pop()?.toLowerCase();
    if (fileExt !== "csv") {
      toast.error("Please upload a CSV (.csv) file");
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
      return;
    }

    // Add a quick validation check for the CSV content
    try {
      const fileContent = await file.text();
      const firstLine = fileContent.split("\n")[0];

      // Check if 'domain' column exists (case-insensitive)
      // Try both comma and semicolon delimiters
      let headers = [];
      if (firstLine.includes(",")) {
        headers = firstLine.split(",").map((h) => h.trim().toLowerCase());
      } else if (firstLine.includes(";")) {
        headers = firstLine.split(";").map((h) => h.trim().toLowerCase());
      } else {
        // If no delimiter is found, try splitting by any whitespace
        headers = firstLine.split(/\s+/).map((h) => h.trim().toLowerCase());
      }

      if (!headers.includes("domain")) {
        toast.error(
          "CSV file must contain a 'domain' column. Please download and use the template.",
        );
        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
        return;
      }
    } catch (error) {
      console.error("Error pre-validating CSV:", error);
      // Continue with upload even if pre-validation fails
    }

    setIsUploading(true);

    try {
      // Convert file to base64
      const fileBase64 = await convertFileToBase64(file);

      // Use the mutation to upload the file
      importDomainsMutation.mutate({
        data: {
          fileName: file.name,
          fileContent: fileBase64,
        },
      });
    } catch (error: any) {
      toast.error(error.message || "Failed to prepare file for upload");
    } finally {
      setIsUploading(false);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <div className="flex w-full max-w-full flex-col items-stretch gap-2 overflow-hidden md:w-auto md:flex-row md:items-center md:gap-3">
      <button
        onClick={downloadTemplate}
        className="inline-flex max-w-full min-w-0 flex-1 cursor-pointer items-center justify-center space-x-1 rounded-lg bg-blue-600 px-2 py-2.5 text-xs font-medium whitespace-nowrap text-white shadow-sm transition-colors duration-200 hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:flex-initial md:space-x-2 md:px-3 md:text-sm lg:px-4"
        disabled={isTemplateDownloading}
      >
        <HiOutlineDocumentArrowDown className="h-4 w-4 flex-shrink-0" />
        <span className="hidden truncate md:inline">
          {isTemplateDownloading ? "Downloading..." : "Download Template"}
        </span>
        <span className="truncate md:hidden">
          {isTemplateDownloading ? "Download..." : "Template"}
        </span>
      </button>

      <label className="inline-flex max-w-full min-w-0 flex-1 cursor-pointer items-center justify-center space-x-1 rounded-lg bg-green-600 px-2 py-2.5 text-xs font-medium whitespace-nowrap text-white shadow-sm transition-colors duration-200 hover:bg-green-700 focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:outline-none md:flex-initial md:space-x-2 md:px-3 md:text-sm lg:px-4">
        <HiOutlineDocumentArrowUp className="h-4 w-4 flex-shrink-0" />
        <span className="hidden truncate md:inline">
          {importDomainsMutation.isPending ? "Uploading..." : "Import Domains"}
        </span>
        <span className="truncate md:hidden">
          {importDomainsMutation.isPending ? "Upload..." : "Import"}
        </span>
        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".csv"
          className="hidden"
          disabled={importDomainsMutation.isPending}
        />
      </label>
    </div>
  );
};
