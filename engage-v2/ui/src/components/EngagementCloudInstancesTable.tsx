import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns";
import { HTMLProps, useEffect, useRef, useState } from "react";
import { HiOutlineCube } from "react-icons/hi";
import { IoIosInformationCircleOutline } from "react-icons/io";

import { CloudInstance } from "../model";
import { CloudInstanceNodeGroups } from "../model";
import { getIconType, nodeTypeColors } from "../utils/assets.tsx";
import EditNodeModal from "./GraphView/EditNodeModal";
import Table from "./Table";

type TableRow = CloudInstanceNodeGroups | CloudInstance;

const columnHelper = createColumnHelper<TableRow>();

const columns = (
  openEditModal: (node: TableRow) => void,
  setSubRowLen: (length: number) => void,
) => [
  columnHelper.accessor("name", {
    header: () => "Name",
    enableResizing: false,
    cell: ({ row, getValue }) => {
      const isCloudNodeGroup = (
        row: TableRow,
      ): row is CloudInstanceNodeGroups => {
        return "cloud_instances" in row;
      };
      if (isCloudNodeGroup(row.original)) {
        return (
          <div
            className={`flex flex-row items-center space-x-2 p-2 ${!row.getCanExpand() && row.getParentRow() ? "ml-4" : ""}`}
          >
            {row.getCanExpand() ? (
              <div className="flex flex-row space-x-2">
                <button
                  {...{
                    onClick: () => {
                      row.getToggleExpandedHandler()();
                      const subRowsLength = row.originalSubRows?.length || 0;
                      setSubRowLen(
                        row.getIsExpanded() ? -subRowsLength : subRowsLength,
                      );
                    },
                    style: { cursor: "pointer" },
                  }}
                >
                  {row.getIsExpanded() ? "-" : "+"}
                </button>
                <HiOutlineCube className="h-6 w-6" />
              </div>
            ) : (
              <HiOutlineCube className="ml-4 h-6 w-6" />
            )}
            <div>{getValue()}</div>
          </div>
        );
      } else {
        return (
          <div className="ml-8 flex flex-row">
            <span
              className={`p-2 ${nodeTypeColors.get(row.original.type)}`}
            ></span>
            <div
              className={`flex flex-row items-center space-x-2 p-2 ${!row.getCanExpand() && row.getParentRow() ? "ml-4" : ""}`}
            >
              <IndeterminateCheckbox
                {...{
                  checked: row.getIsSelected(),
                  indeterminate: row.getIsSomeSelected(),
                  onChange: row.getToggleSelectedHandler(),
                }}
              />
              {getIconType(row.original.type)}
              <div>{getValue()}</div>
            </div>
          </div>
        );
      }
    },
  }),
  columnHelper.accessor("id", {
    header: () => "ID",
    cell: (info) => <div className="pl-4">{info.getValue()}</div>,
  }),
  columnHelper.accessor("cloud_instance_state", {
    header: () => "Cloud Instance State",
    cell: (info) => (
      <div className="pl-4">{(info.getValue() as string)?.toUpperCase()}</div>
    ),
  }),
  columnHelper.accessor("cloud_instance_id", {
    header: () => "Cloud Instance ID",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("ci_deployment_status", {
    header: () => "Deployment Status",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("type", {
    header: () => "Type",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("operating_system_image_id", {
    header: () => "Operating System Image ID",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("provider", {
    header: () => "Provider",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),

  columnHelper.accessor("region", {
    header: () => "Region",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("public_ipv4_address", {
    header: () => "Public IPv4 Address",
    cell: (info) => <div className="pl-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("open_ports", {
    header: () => "Open Ports",
    cell: (info) => {
      const openPorts = info.getValue() as number[];
      return openPorts ? (
        <div className="pl-4">{openPorts.join(", ")}</div>
      ) : null;
    },
  }),
  columnHelper.accessor("created_at", {
    header: () => "Created",
    cell: (info) => (
      <div className="pl-4">
        {format(new Date(info.getValue() as string), "dd-MMM-yyyy, HH:mm")}
      </div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  columnHelper.accessor("updated_at", {
    header: () => "Last Update",
    cell: (info) => (
      <div className="pl-4">
        {format(new Date(info.getValue() as string), "dd-MMM-yyyy, HH:mm")}
      </div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  {
    id: "infoColumn",
    header: () => "",
    cell: ({ row }: { row: { original: TableRow } }) => {
      const isCloudNodeGroup = (
        row: TableRow,
      ): row is CloudInstanceNodeGroups => {
        return "cloud_instances" in row;
      };
      if (!isCloudNodeGroup(row.original)) {
        return (
          <div>
            <IoIosInformationCircleOutline
              className="h-5 w-5"
              onClick={() => openEditModal(row.original)}
            />
          </div>
        );
      }
    },
  },
];

function IndeterminateCheckbox({
  indeterminate,
  className = "",
  ...rest
}: { indeterminate?: boolean } & HTMLProps<HTMLInputElement>) {
  const ref = useRef<HTMLInputElement>(null!);

  useEffect(() => {
    if (typeof indeterminate === "boolean") {
      ref.current.indeterminate = !rest.checked && indeterminate;
    }
  }, [ref, indeterminate, rest.checked]);

  return (
    <input
      type="checkbox"
      ref={ref}
      className={className + " cursor-pointer"}
      {...rest}
    />
  );
}

type Props = {
  nodeGroups: CloudInstanceNodeGroups[];
  engagementName: string;
};

export default function EngagementCloudInstancesTable({
  nodeGroups,
  engagementName,
}: Props) {
  const getSubRows = (row: TableRow): TableRow[] | undefined => {
    if ("cloud_instances" in row) {
      return row.cloud_instances?.map((cloudInstance) => ({
        ...cloudInstance,
        id: cloudInstance.id,
        name: cloudInstance.name,
        is_active: null,
        type: cloudInstance.type,
      }));
    }
    return undefined;
  };
  const [isOpenEditModal, setIsOpenEditModal] = useState<boolean>(false);
  const [selectedRow, setSelectedRow] = useState<TableRow | null>(null);
  const [subRowLen, setSubRowLen] = useState<number>(0);

  const openEditModal = (row: TableRow) => {
    setSelectedRow(row);
    setIsOpenEditModal(true);
  };

  const closeEditModal = () => {
    setSelectedRow(null);
    setIsOpenEditModal(false);
  };

  return (
    <>
      <Table
        data={nodeGroups}
        columns={columns(openEditModal, setSubRowLen)}
        getSubRows={getSubRows}
        subRowLen={subRowLen}
      />
      <EditNodeModal
        isOpen={isOpenEditModal}
        closeModal={closeEditModal}
        nodeDetails={selectedRow}
        isNodeGraph={false}
        engagementName={engagementName}
      />
    </>
  );
}
