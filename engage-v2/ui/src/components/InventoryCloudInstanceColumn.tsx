import { createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns/format";
import { IoIosInformationCircleOutline } from "react-icons/io";

import { InventoryCloudInstance } from "../model";

type TableRow = InventoryCloudInstance;

const columnHelper = createColumnHelper<TableRow>();

export const cloud_instance_columns = (
  openEditModal: (node: TableRow) => void,
) => [
    columnHelper.accessor("name", {
      header: () => "Name",
      cell: (info) => <div className="py-4 min-w-[120px] max-w-[180px] truncate">{info.getValue()}</div>,
    }),
    columnHelper.accessor("cloud_instance_state", {
      header: () => "State",
      cell: (info) => <div className="py-4 min-w-[80px] max-w-[120px] truncate">{info.getValue()?.toUpperCase()}</div>,
    }),
    columnHelper.accessor("cloud_instance_id", {
      header: () => "Instance ID",
      cell: (info) => <div className="py-4 min-w-[100px] max-w-[150px] truncate">{info.getValue()}</div>,
    }),
    columnHelper.accessor("ci_deployment_status", {
      header: () => "Deploy Status",
      cell: (info) => <div className="py-4 min-w-[90px] max-w-[120px] truncate">{info.getValue()}</div>,
    }),
    columnHelper.accessor("operating_system_image_id", {
      header: () => "OS Image ID",
      cell: (info) => <div className="py-4 min-w-[100px] max-w-[140px] truncate">{info.getValue() as string}</div>,
    }),
    columnHelper.accessor("provider", {
      header: () => "Provider",
      cell: (info) => <div className="py-4 min-w-[70px] max-w-[100px] truncate">{info.getValue() as string}</div>,
    }),

    columnHelper.accessor("region", {
      header: () => "Region",
      cell: (info) => <div className="py-4 min-w-[70px] max-w-[100px] truncate">{info.getValue() as string}</div>,
    }),
    columnHelper.accessor("public_ipv4_address", {
      header: () => "Public IP",
      cell: (info) => <div className="py-4 min-w-[100px] max-w-[140px] truncate">{info.getValue() as string}</div>,
    }),
    columnHelper.accessor("open_ports", {
      header: () => "Ports",
      cell: (info) => {
        const openPorts = info.getValue() as number[];
        return openPorts ? (
          <div className="py-4 min-w-[80px] max-w-[120px] truncate">{openPorts.join(", ")}</div>
        ) : <div className="py-4">-</div>;
      },
    }),
    columnHelper.accessor("updated_at", {
      header: () => "Updated",
      cell: (info) => (
        <div className="py-4 min-w-[100px] max-w-[130px] truncate">
          {format(new Date(info.getValue() as string), "MMM d, yy")}
        </div>
      ),
      filterFn: "dateRange",
      meta: {
        filterVariant: "date"
      }
    }),
    columnHelper.accessor("client_name", {
      header: () => "Client",
      cell: (info) => <div className="py-4 min-w-[80px] max-w-[120px] truncate">{info.getValue() as string}</div>,
    }),
    {
      id: "infoColumn",
      header: () => "",
      cell: ({ row }: { row: { original: TableRow } }) => (
        <div className="py-4">
          <IoIosInformationCircleOutline
            className="h-5 w-5"
            onClick={() => openEditModal(row.original)}
          />
        </div>
      ),
    },
  ];
