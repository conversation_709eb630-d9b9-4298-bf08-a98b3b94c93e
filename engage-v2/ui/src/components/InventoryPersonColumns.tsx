import { RankingInfo } from "@tanstack/match-sorter-utils";
import { FilterFn, createColumnHelper } from "@tanstack/react-table";
import { format } from "date-fns/format";
import { IoIosInformationCircleOutline } from "react-icons/io";

import { Person } from "../model";

type TableRow = Person;

const columnHelper = createColumnHelper<TableRow>();

declare module "@tanstack/react-table" {
  //add fuzzy filter to the filterFns
  interface FilterFns {
    fuzzy: FilterFn<unknown>;
  }
  interface FilterMeta {
    itemRank: RankingInfo;
  }
}

export const inventoryPersonColumns = (
  openEditModal: (node: TableRow) => void,
) => [
  columnHelper.accessor("first_name", {
    header: () => "First Name",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
    //  filterFn: 'fuzzy',
  }),
  columnHelper.accessor("last_name", {
    header: () => "Last Name",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
    filterFn: "fuzzy",
    //sortingFn: fuzzySort,
  }),
  columnHelper.accessor("email", {
    header: () => "Email",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
    //  filterFn: 'fuzzy'
  }),
  columnHelper.accessor("company", {
    header: () => "Company",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
    //  filterFn: 'fuzzy'
  }),
  columnHelper.accessor("title", {
    header: () => "Title",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
    //  filterFn: 'fuzzy'
  }),
  columnHelper.accessor("client_name", {
    header: () => "Client Name",
    cell: (info) => <div className="py-4">{info.getValue() as string}</div>,
  }),
  columnHelper.accessor("updated_at", {
    header: () => "Last Updated",
    cell: (info) => (
      <div className="py-4">
        {format(new Date(info.getValue() as string), "dd-MMM-yyyy, HH:mm")}
      </div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date",
    },
  }),
  {
    id: "infoColumn",
    header: () => "",
    cell: ({ row }: { row: { original: TableRow } }) => (
      <div className="py-4">
        <IoIosInformationCircleOutline
          className="h-5 w-5"
          onClick={() => openEditModal(row.original)}
        />
      </div>
    ),
  },
];
