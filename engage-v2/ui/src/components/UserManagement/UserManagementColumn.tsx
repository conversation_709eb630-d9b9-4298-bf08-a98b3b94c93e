import { Row, Table, createColumnHelper } from "@tanstack/react-table";
import { IoIosInformationCircleOutline } from "react-icons/io";

import { EngagementUser } from "../../model";
import { IndeterminateCheckbox } from "../../utils/utils";
import { capitalizeFirstLetter } from "../../utils/validationutils";

export type TableRow = EngagementUser;

const columnHelper = createColumnHelper<TableRow>();

export const userManagementColumns = (
  openEditModal: (node: TableRow) => void,
) => [
  {
    id: "checkboxColumn",
    header: ({ table }: { table: Table<TableRow> }) => (
      <div className="pl-4">
        <IndeterminateCheckbox
          {...{
            checked: table.getIsAllRowsSelected(),
            indeterminate: table.getIsSomeRowsSelected(),
            onChange: table.getToggleAllRowsSelectedHandler(),
          }}
        />
      </div>
    ),
    cell: ({ row }: { row: Row<TableRow> }) => (
      <div className="pl-4">
        <IndeterminateCheckbox
          {...{
            checked: row.getIsSelected(),
            indeterminate: row.getIsSomeSelected(),
            onChange: row.getToggleSelectedHandler(),
          }}
        />
      </div>
    ),
  },
  columnHelper.accessor("id", {
    header: () => "ID",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
  }),
  columnHelper.accessor("full_name", {
    header: () => "Name",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
  }),
  columnHelper.accessor("username", {
    header: () => "Username",
    cell: (info) => (
      <div className="py-4 pr-2 text-wrap break-words">{info.getValue()}</div>
    ),
  }),
  columnHelper.accessor("app_role", {
    header: () => "Role",
    cell: (info) => (
      <div className="py-4">
        {capitalizeFirstLetter(info.getValue() as string)}
      </div>
    ),
  }),
  {
    id: "infoColumn",
    header: () => "",
    cell: ({ row }: { row: { original: TableRow } }) => (
      <div className="py-4">
        <IoIosInformationCircleOutline
          className="h-5 w-5"
          onClick={() => openEditModal(row.original)}
        />
      </div>
    ),
  },
];
