import { Link } from "@tanstack/react-router";
import { ColumnDef, createColumnHelper } from "@tanstack/react-table";
import Tippy from "@tippyjs/react";
import { format } from "date-fns/format";

import { DeploymentModel } from "../../model";
import {
  getStatusColor,
  getStatusIcon,
  getUserBgColor,
} from "../../utils/assets";

type TableRow = DeploymentModel;

const columnHelper = createColumnHelper<TableRow>();

export const DeploymentColumns: ColumnDef<TableRow, string>[] = [
  columnHelper.accessor("status", {
    header: () => "Status",
    cell: (info) => (
      <div className="flex py-4">
        <span
          className={`flex flex-row content-center items-center rounded-full px-2 py-1 shadow-md ${getStatusColor(info.getValue())}`}
        >
          {getStatusIcon(info.getValue())}
          {info
            .getValue()
            .toLowerCase()
            .replace(/[-_]/g, " ")
            .replace(/\b\w/g, (char: string) => char.toUpperCase())}
        </span>
      </div>
    ),
  }),
  columnHelper.accessor("id", {
    header: () => "Deployment ID",
    cell: (info) => {
      return (
        <div className="py-4">
          <Link
            to={`/deployments/$deploymentId`}
            params={{ deploymentId: info.row.original.id }}
            className="hover:text-purple-600"
          >
            <span className="cursor-pointer hover:text-purple-600">
              {info.getValue()}
            </span>
          </Link>
        </div>
      );
    },
  }),
  columnHelper.accessor("title", {
    header: () => "Engagement",
    cell: (info) => {
      return (
        <div className="py-4">
          <Link
            to={`/engagements/$engagementId`}
            params={{ engagementId: info.row.original.engagement_id }}
            className="hover:text-purple-600"
          >
            {info.getValue()}
          </Link>
        </div>
      );
    },
  }),
  columnHelper.accessor("node_name", {
    header: () => "Node Name",
    cell: (info) => <div className="py-4">{info.getValue()}</div>,
  }),
  columnHelper.accessor("username", {
    header: () => "Created by",
    cell: (info) => {
      const bgColor = getUserBgColor(info.row.original.user_id);
      return (
        <Tippy content={info.getValue()} placement="top">
          <div
            className={`${bgColor} h-11 w-11 cursor-pointer content-center rounded-full text-center text-white`}
          >
            {info.getValue().charAt(0).toUpperCase()}
          </div>
        </Tippy>
      );
    },
  }),
  columnHelper.accessor("created_at", {
    header: () => "Created At",
    cell: (info) => (
      <div className="py-4">
        {format(new Date(info.getValue() as string), "dd-MMM-yyyy, HH:mm")}
      </div>
    ),
    filterFn: "dateRange",
    meta: {
      filterVariant: "date"
    }
  }),
];
