import {
  Dialog,
  DialogPanel,
  DialogTitle,
  Transition,
  TransitionChild,
} from "@headlessui/react";
import { Fragment, useEffect, useState } from "react";
import { HiChevronLeft } from "react-icons/hi";

import { capitalizeFirstLetter } from "../../utils/validationutils.ts";
import CloudInstanceFlow from "./CloudInstance/CloudInstanceFlow.tsx";
import EmailAddressFlow from "./EmailAddress/EmailAddressFlow.tsx";
import HostFlow from "./Host/HostFlow.tsx";
import PersonFlow from "./Person/PersonFlow.tsx";
import SelectNodeType from "./SelectNodeType.tsx";
import URLFlow from "./URL/URLFlow.tsx";
import { CreateNodeScreen, NodeType } from "./types.ts";

type CreateNodeModalProps = {
  engagementID: string;
  nodeGroupID?: string;
  isOpen: boolean;
  closeModal: () => void;
};

export default function CreateNodeModal({
  engagementID,
  nodeGroupID,
  isOpen,
  closeModal,
}: CreateNodeModalProps) {
  const [nodeType, setNodeType] = useState<NodeType | null>(null);
  const [createNodeScreen, setCreateNodeScreen] = useState<CreateNodeScreen>(
    CreateNodeScreen.SelectNodeType,
  );

  useEffect(() => {
    if (nodeType !== null) {
      updateCreateNodeScreen();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [nodeType]);

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setCreateNodeScreen(CreateNodeScreen.SelectNodeType);
      setNodeType(null);
    }
  }, [isOpen]);

  function updateCreateNodeScreen() {
    if (nodeType === NodeType.CloudInstance) {
      setCreateNodeScreen(CreateNodeScreen.AddCloudInstanceDetails);
    }

    if (nodeType === NodeType.EmailAddress) {
      setCreateNodeScreen(CreateNodeScreen.AddEmailAddressDetails);
    }

    if (nodeType === NodeType.Host) {
      setCreateNodeScreen(CreateNodeScreen.AddHostDetails);
    }

    if (nodeType === NodeType.URL) {
      setCreateNodeScreen(CreateNodeScreen.AddURLDetails);
    }

    if (nodeType === NodeType.Person) {
      setCreateNodeScreen(CreateNodeScreen.AddPersonDetails);
    }
  }

  function formatNodeType(nodeType: string) {
    const words = nodeType?.toLowerCase().split("_");

    for (let i = 0; i < words?.length; i++) {
      words[i] = capitalizeFirstLetter(words[i]);
    }

    return words.join(" ");
  }

  const getTitle = () => `Create ${nodeType ? formatNodeType(nodeType) : ""}`;

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog
        as="div"
        className="relative z-30"
        onClose={() => {
          closeModal();
        }}
      >
        <TransitionChild
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black/25" />
        </TransitionChild>

        <div className="fixed inset-0 overflow-y-auto dark:text-white">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <TransitionChild
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <DialogPanel className="w-11/12 transform overflow-hidden rounded-2xl bg-white p-8 text-left align-middle shadow-xl transition-all sm:w-10/12 md:w-8/12 lg:p-10 dark:bg-[#364151]">
                <DialogTitle
                  as="h3"
                  className="flex flex-row items-center space-x-4 text-xl font-bold text-black md:space-x-0 lg:text-3xl dark:text-white"
                >
                  {createNodeScreen !== CreateNodeScreen.SelectNodeType && (
                    <button
                      className="flex flex-row text-gray-600 hover:text-gray-800 md:flex md:hidden"
                      onClick={() => {
                        setCreateNodeScreen(CreateNodeScreen.SelectNodeType);
                        setNodeType(null);
                      }}
                    >
                      <HiChevronLeft className="h-4 w-4 dark:text-white" />
                    </button>
                  )}
                  <div>
                    {createNodeScreen === CreateNodeScreen.SelectNodeType
                      ? null
                      : getTitle()}
                  </div>
                </DialogTitle>
                <div id="dialog-content" className="space-y-8">
                  {createNodeScreen === CreateNodeScreen.SelectNodeType && (
                    <SelectNodeType
                      closeModal={closeModal}
                      nodeType={nodeType}
                      setNodeType={setNodeType}
                      updateCreateNodeScreen={updateCreateNodeScreen}
                    />
                  )}
                  {createNodeScreen ===
                    CreateNodeScreen.AddCloudInstanceDetails && (
                    <CloudInstanceFlow
                      engagementID={engagementID}
                      nodeGroupID={nodeGroupID}
                      setCreateNodeScreen={setCreateNodeScreen}
                      closeModal={closeModal}
                      setNodeType={setNodeType}
                    />
                  )}
                  {createNodeScreen ===
                    CreateNodeScreen.AddEmailAddressDetails && (
                    <EmailAddressFlow
                      engagementID={engagementID}
                      nodeGroupID={nodeGroupID}
                      setCreateNodeScreen={setCreateNodeScreen}
                      closeModal={closeModal}
                      setNodeType={setNodeType}
                    />
                  )}
                  {createNodeScreen === CreateNodeScreen.AddHostDetails && (
                    <HostFlow
                      engagementID={engagementID}
                      nodeGroupID={nodeGroupID}
                      setCreateNodeScreen={setCreateNodeScreen}
                      closeModal={closeModal}
                      setNodeType={setNodeType}
                    />
                  )}
                  {createNodeScreen === CreateNodeScreen.AddURLDetails && (
                    <URLFlow
                      engagementID={engagementID}
                      nodeGroupID={nodeGroupID}
                      setCreateNodeScreen={setCreateNodeScreen}
                      closeModal={closeModal}
                      setNodeType={setNodeType}
                    />
                  )}
                  {createNodeScreen === CreateNodeScreen.AddPersonDetails && (
                    <PersonFlow
                      engagementID={engagementID}
                      nodeGroupID={nodeGroupID}
                      setCreateNodeScreen={setCreateNodeScreen}
                      closeModal={closeModal}
                      setNodeType={setNodeType}
                    />
                  )}
                </div>
                <div
                  id="node-selection-actions"
                  className="flex flex-row items-center justify-end space-x-4 pt-4"
                ></div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
