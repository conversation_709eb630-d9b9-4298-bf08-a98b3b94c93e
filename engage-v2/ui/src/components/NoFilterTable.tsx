import { RankingInfo, rankItem } from "@tanstack/match-sorter-utils";
import {
  ColumnDef,
  ColumnFiltersState,
  ExpandedState,
  FilterFn,
  SortingState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
import { HiChevronLeft, HiChevronRight } from "react-icons/hi";
import { HiMagnifyingGlass, HiXMark } from "react-icons/hi2";
import { TbFilterOff } from "react-icons/tb";

import { useTheme } from "../context/ThemeProvider";

interface TableProps<T> {
  data: T[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columns: ColumnDef<T, any>[];
  getSubRows?: (row: T) => T[] | undefined;
  subRowLen?: number;
}

declare module "@tanstack/react-table" {
  interface FilterFns {
    fuzzy: FilterFn<unknown>;
    dateRange: FilterFn<unknown>;
  }

  interface FilterMeta {
    itemRank: RankingInfo;
  }
}

function createFuzzyFilter<T>(): FilterFn<T> {
  return (row, columnId, value, addMeta) => {
    const itemRank = rankItem(row.getValue(columnId), value);
    addMeta({ itemRank });
    return itemRank.passed;
  };
}

function createDateRangeFilter<T>(): FilterFn<T> {
  return (row, columnId, value) => {
    const [startDate, endDate] = value || [undefined, undefined];
    const cellValue = row.getValue(columnId);

    if (!cellValue) return true;

    // Handle different date formats
    let rowDate: Date;
    if (typeof cellValue === 'string') {
      // Handle DD.MM.YYYY format
      if (cellValue.includes('.') && cellValue.split('.').length === 3) {
        const parts = cellValue.split('.');
        if (parts.length === 3 && parts[2].length === 4) {
          // Convert DD.MM.YYYY to YYYY-MM-DD for Date constructor
          rowDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
        } else {
          rowDate = new Date(cellValue);
        }
      } else {
        rowDate = new Date(cellValue);
      }
    } else {
      rowDate = new Date(cellValue as string | number | Date);
    }

    if (isNaN(rowDate.getTime())) return true;

    if (startDate && rowDate < startDate) return false;
    if (endDate && rowDate > endDate) return false;

    return true;
  };
}

export function DebouncedInput({
  value: initialValue,
  onChange,
  debounce = 500,
  ...props
}: {
  value: string | number;
  onChange: (value: string | number) => void;
  debounce?: number;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange">) {
  const [value, setValue] = useState(initialValue);

  useEffect(() => {
    // Ensure we never set undefined or null
    setValue(initialValue ?? '');
  }, [initialValue]);

  useEffect(() => {
    const timeout = setTimeout(() => onChange(value), debounce);
    return () => clearTimeout(timeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  return (
    <input
      {...props}
      value={value}
      onChange={(e) => setValue(e.target.value)}
    />
  );
}

function NoFilterTable<T>({
  data,
  columns,
  getSubRows,
  subRowLen = 0,
}: TableProps<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [expanded, setExpanded] = useState<ExpandedState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  
  const table = useReactTable<T>({
    data,
    columns,
    filterFns: {
      fuzzy: createFuzzyFilter(),
      dateRange: createDateRangeFilter()
    },
    state: { sorting, expanded, columnFilters, globalFilter, pagination },
    globalFilterFn: createFuzzyFilter(),
    getExpandedRowModel: getExpandedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    onExpandedChange: setExpanded,
    getSubRows,
  });
  
  const { isDarkMode } = useTheme();
  
  // Function to reset all filters
  const resetAllFilters = () => {
    setGlobalFilter("");
    setColumnFilters([]);
    table.resetColumnFilters();
    table.resetGlobalFilter();
  };
  
  // Check if any filters are active
  const hasActiveFilters = globalFilter !== "" || columnFilters.length > 0;
  
  useEffect(() => {
    setPagination((prevPagination) => ({
      ...prevPagination,
      pageSize: prevPagination.pageSize + subRowLen,
    }));
  }, [subRowLen]);

  return (
    <div className="overflow-x-auto md:p-2">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <div className="flex w-full max-w-lg flex-row items-center rounded-sm border border-solid border-gray-400 bg-white px-4 focus-within:outline-hidden focus-within:ring-2 focus-within:ring-purple-500 dark:bg-[#303c4f]">
          <HiMagnifyingGlass
            className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5`}
          />
          <DebouncedInput
            value={globalFilter}
            onChange={(value) => setGlobalFilter(String(value))}
            className="font-lg w-full bg-transparent p-2 focus:outline-hidden focus:ring-0"
            placeholder="Search deployments"
          />
          {globalFilter && (
            <button 
              onClick={() => setGlobalFilter("")}
              className="text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-white"
            >
              <HiXMark className="h-5 w-5" />
            </button>
          )}
        </div>
        
        {hasActiveFilters && (
          <button
            onClick={resetAllFilters}
            className="mt-2 md:mt-0 flex items-center space-x-1 px-3 py-2 rounded-md border border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700 dark:text-white transition-colors"
            title="Reset all filters"
          >
            <TbFilterOff className="h-4 w-4" />
            <span>Reset Filters</span>
          </button>
        )}
      </div>
      <div className="relative mt-4 overflow-x-auto w-full">
        <table className="w-full table-auto">
          <thead className="sticky top-0 z-10 bg-white dark:bg-[#354155] dark:text-white">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className={`${header.id !== "infoColumn" && header.id !== "checkboxColumn" ? "min-w-[130px]" : ""} max-w-[250px] pb-2 pr-8 text-left text-sm font-normal dark:text-white`}
                  >
                    <div className="font-semibold">
                      {flexRender(
                        header.column.columnDef.header,
                        header.getContext(),
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="dark:text-white">
            {table.getRowModel().rows.map((row, rowIndex) => (
              <tr
                key={row.id}
                className={`${rowIndex % 2 === 1 ? "bg-slate-50 dark:bg-slate-600" : ""}`}
              >
                {row.getVisibleCells().map((cell) => (
                  <td
                    key={cell.id}
                    className={`${cell.id.includes("infoColumn") ? "cursor-pointer" : ""} max-w-[250px] py-2 px-4 text-left text-sm font-normal whitespace-nowrap overflow-hidden text-ellipsis`}
                  >
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      <div className="flex justify-between pt-4">
        <div className="flex gap-2">
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <HiChevronLeft
              className={`${isDarkMode ? "text-white" : "text-black"} ${!table.getCanPreviousPage() ? "text-gray-400" : "text-black dark:text-white"} h-5 w-5 cursor-pointer`}
            />
          </button>
          <div className="rounded-sm bg-purple-700 px-3 py-2 text-white dark:bg-purple-500">
            {table.getState().pagination.pageIndex + 1}
          </div>
          <button
            className={
              !table.getCanNextPage() ? "text-gray-100" : "text-grey-500"
            }
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <HiChevronRight
              className={`${isDarkMode ? "text-white" : "text-black"} ${!table.getCanNextPage() ? "text-gray-400" : "text-black dark:text-white"} h-5 w-5 cursor-pointer`}
            />
          </button>
        </div>
        <div className="align-center">
          <span className="content-bottom text-xs font-medium text-gray-400 dark:text-gray-200">
            Showing {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()} pages
          </span>
        </div>
      </div>
    </div>
  );
}

export default NoFilterTable;
