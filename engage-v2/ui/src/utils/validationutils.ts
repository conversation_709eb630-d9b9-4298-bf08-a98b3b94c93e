import { z } from "zod";

const regex = {
  companyName: /^[A-Za-z0-9 &.-]{2,50}$/,
  lettersAndNumbers: /^[A-Za-z0-9 ]{2,30}$/,
  ipAddress:
    /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
};

export const capitalizeFirstLetter = (string: string) =>
  string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();

export const errorMessage = {
  required: (field: string) => `${capitalizeFirstLetter(field)} is required.`,
  invalidInstanceName:
    "Instance name must be between 1 and 256 characters (letters, numbers, spaces representable in UTF-8, and the following characters: _ . : / = + - @)",
  invalidAzureResourceName:
    "Invalid name for Azure resource. Only letters, numbers, dots (.), dashes (-) are allowed.",
  invalidCompanyName:
    "Invalid Company name. Only letters, - & . and numbers are allowed.",
  invalidEmail: "Invalid email address.",
  invalidIP: "Invalid IP address format.",
  invalidName: "Invalid name. Only letters , . ' - are allowed.",
  invalidAltName: "Invalid name. Only letters and numbers are allowed.",
  invalidPhoneNumber: "Invalid phone number format.",
  invalidTitle: "Invalid Title. Only letters , . ' - are allowed.",
  invalidUrl: "Invalid URL.",
  invalidCustomUsername:
    "Invalid Username. Only lowercase characters are allowed",
  maxLength: (field: string, maxLen: number) =>
    `${capitalizeFirstLetter(field)} must not be more than ${maxLen} characters.`,
  minLength: (field: string, minLen: number) =>
    `${capitalizeFirstLetter(field)} must be more than ${minLen} characters.`,
};

export const validationSchemaType = {
  instanceName: z
    .string()
    .regex(/^[a-zA-Z0-9 _.:/=+\-@]{1,256}$/, errorMessage.invalidInstanceName),
  azureInstanceName: z
    .string()
    .regex(/^[a-zA-Z0-9.-]{1,50}$/, {
      message: errorMessage.invalidAzureResourceName,
    }),
  name: z.string().max(50, errorMessage.maxLength("name", 50)),
  email: z.string().email(errorMessage.invalidEmail),
  ipAddress: z
    .array(z.string().regex(regex.ipAddress, errorMessage.invalidIP))
    .optional(),
  ipAddress1: z.string().regex(regex.ipAddress, errorMessage.invalidIP),
  alternativeNames: z
    .array(
      z
        .string()
        .min(1, errorMessage.required("alternative name"))
        .max(50, errorMessage.maxLength("alternative name", 50))
        .regex(regex.lettersAndNumbers, errorMessage.invalidAltName),
    )
    .optional(),
  alternativeNames1: z
    .string()
    .min(1, errorMessage.required("alternative name"))
    .max(50, errorMessage.maxLength("alternative name", 50))
    .regex(regex.lettersAndNumbers, errorMessage.invalidAltName),
  companyName: z
    .string()
    .min(2, errorMessage.required("company name"))
    .max(50, errorMessage.maxLength("company name", 50))
    .regex(regex.companyName, errorMessage.invalidCompanyName),
  title: z
    .string()
    .min(2, errorMessage.required("title"))
    .max(50, errorMessage.maxLength("title", 50)),
  url: z.string().url(errorMessage.invalidUrl),
  customUsername: z
    .string()
    .min(2, errorMessage.minLength("user name", 2))
    .max(32, errorMessage.maxLength("user name", 32))
    .refine(
      (val) => !/^[0-9]/.test(val), // Ensure it does not start with a number
      {
        message: "Username cannot start with a number.",
        path: ["customUsername"],
      },
    )
    .refine(
      (val) => !/^[A-Z]/.test(val), // Ensure it does not start with a capital letter
      {
        message: "Username cannot start with a capital letter.",
        path: ["customUsername"],
      },
    )
    .refine(
      (val) => !/^(?:root|bin|daemon|sys|admin)/.test(val), // Ensure it does not start with a capital letter
      {
        message:
          "Username cannot start with reserved words like 'root', 'bin', 'daemon', 'sys', or 'admin'.",
        path: ["customUsername"],
      },
    )
    .refine(
      (val) => !/[.]/.test(val), // Ensure it does not contain a dot (.)
      {
        message: "Username cannot contain dots (.)",
        path: ["customUsername"],
      },
    )
    .refine(
      (val) => !/[@!#]/.test(val), // Ensure it does not contain special characters @, !, or #
      {
        message: "Username cannot contain special characters like @, !, or #.",
        path: ["customUsername"],
      },
    ),
};

// Validation schemas for different node types
export const nodeValidationSchemas = {
  person: z.object({
    first_name: z
      .string()
      .min(1, errorMessage.required("first name"))
      .max(50, errorMessage.maxLength("first name", 50)),
    last_name: z
      .string()
      .max(50, errorMessage.maxLength("last name", 50))
      .optional(),
    title: z.string().max(50, errorMessage.maxLength("title", 50)).optional(),
    company: z
      .string()
      .max(50, errorMessage.maxLength("company", 50))
      .optional(),
    email: z.string().email(errorMessage.invalidEmail).optional(),
  }),
  url: z.object({
    url: validationSchemaType.url,
  }),
  host: z.object({
    name: z
      .string()
      .min(1, errorMessage.required("name"))
      .max(50, errorMessage.maxLength("name", 50)),
    ip_addresses: z
      .array(z.string().regex(regex.ipAddress, errorMessage.invalidIP))
      .optional(),
    alternative_names: z
      .array(
        z
          .string()
          .min(1, errorMessage.required("alternative name"))
          .max(50, errorMessage.maxLength("alternative name", 50))
          .regex(regex.lettersAndNumbers, errorMessage.invalidAltName),
      )
      .optional(),
  }),
  email_address: z.object({
    email_address: validationSchemaType.email,
  }),
  cloud_instance: z.object({
    name: validationSchemaType.instanceName,
  }),
};

// Helper function to validate form data and return errors
export const validateFormData = <T extends Record<string, unknown>>(
  data: T,
  schema: z.ZodSchema<T>,
): { isValid: boolean; errors: Record<string, string> } => {
  const result = schema.safeParse(data);

  if (result.success) {
    return { isValid: true, errors: {} };
  }

  const errors: Record<string, string> = {};
  result.error.issues.forEach((issue) => {
    const path = issue.path.join(".");
    errors[path] = issue.message;
  });

  return { isValid: false, errors };
};
