/**
 * Security utilities for XSS protection and input sanitization
 */

/**
 * Escapes HTML characters to prevent XSS attacks
 * @param text - The text to escape
 * @returns Escaped text safe for HTML rendering
 */
export function escapeHtml(text: string | null | undefined): string {
  if (!text) return '';
  
  const escapeMap: { [key: string]: string } = {
    '<': '&lt;',
    '>': '&gt;',
    '"': '&quot;',
    "'": '&#x27;',
    '&': '&amp;',
    '/': '&#x2F;',
    '`': '&#x60;',
    '=': '&#x3D;'
  };
  
  return text.replace(/[<>"'&/`=]/g, (match) => escapeMap[match]);
}

/**
 * Sanitizes instance type strings specifically
 * Allows alphanumeric, dots, hyphens, and underscores only
 * @param instanceType - The instance type to sanitize
 * @returns Sanitized instance type
 */
export function sanitizeInstanceType(instanceType: string | null | undefined): string {
  if (!instanceType) return '';
  
  // Remove any characters that aren't alphanumeric, dots, hyphens, or underscores
  const sanitized = instanceType.replace(/[^a-zA-Z0-9.\-_]/g, '');
  
  // Limit length to prevent abuse
  return sanitized.substring(0, 50);
}

/**
 * Validates if a string contains potentially malicious content
 * @param input - The input to validate
 * @returns true if input appears safe, false if potentially malicious
 */
export function isInputSafe(input: string | null | undefined): boolean {
  if (!input) return true;
  
  // Check for common XSS patterns
  const dangerousPatterns = [
    /<script/i,
    /javascript:/i,
    /on\w+\s*=/i,
    /<iframe/i,
    /<object/i,
    /<embed/i,
    /<link/i,
    /<meta/i,
    /data:text\/html/i,
    /vbscript:/i,
    /expression\s*\(/i
  ];
  
  return !dangerousPatterns.some(pattern => pattern.test(input));
}

/**
 * Sanitizes user input for display purposes
 * @param input - The input to sanitize
 * @param maxLength - Maximum allowed length (default: 100)
 * @returns Sanitized input
 */
export function sanitizeUserInput(input: string | null | undefined, maxLength: number = 100): string {
  if (!input) return '';
  
  // First escape HTML
  let sanitized = escapeHtml(input);
  
  // Limit length
  sanitized = sanitized.substring(0, maxLength);
  
  return sanitized;
}

/**
 * Content Security Policy helper for dynamic content
 * @param content - Content to validate against CSP
 * @returns true if content is CSP compliant
 */
export function isCSPCompliant(content: string): boolean {
  // Check for inline scripts, styles, or event handlers
  const cspViolations = [
    /<script(?:\s[^>]*)?>.*?<\/script>/gi,
    /<style(?:\s[^>]*)?>.*?<\/style>/gi,
    /on\w+\s*=\s*["'][^"']*["']/gi,
    /javascript:\s*[^"'\s]+/gi,
    /data:\s*text\/html/gi
  ];
  
  return !cspViolations.some(pattern => pattern.test(content));
}
