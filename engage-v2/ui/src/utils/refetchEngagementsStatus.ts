export const createRefetchStatus =
  <T extends Record<string, any>>(
    collectionKey: string,
    options: { refetchIntervalSeconds?: number; statusField?: string } = {},
  ) =>
  (data: any): number | false => {
    const _interval = options.refetchIntervalSeconds ?? 30000;
    const statusField = options.statusField;

    // Try to extract the collection
    let collection = data?.state?.data?.[collectionKey];
    if (!collection && Array.isArray(data?.state?.data)) {
      collection = data.state.data;
    }
    if (!collection && Array.isArray(data)) {
      collection = data;
    }

    if (!collection || !Array.isArray(collection)) {
      return _interval;
    }

    const allowedStatuses = ["SUCCESS", "ACTIVE", "ERROR", "WARNING"];
    const shouldRefetch = collection.some((item: T) => {
      const status = statusField ? item[statusField] : undefined;
      return !allowedStatuses.includes(status);
    });

    return shouldRefetch ? _interval : false;
  };
