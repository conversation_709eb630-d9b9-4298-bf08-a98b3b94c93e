import { 
  sanitizeInstanceType, 
  escapeHtml, 
  isInputSafe, 
  sanitizeUserInput,
  isCSPCompliant 
} from '../security';

describe('Security Utils', () => {
  describe('sanitizeInstanceType', () => {
    it('should allow valid instance types', () => {
      expect(sanitizeInstanceType('t2.micro')).toBe('t2.micro');
      expect(sanitizeInstanceType('m5.large')).toBe('m5.large');
      expect(sanitizeInstanceType('custom_alpha')).toBe('custom_alpha');
      expect(sanitizeInstanceType('c5n.xlarge')).toBe('c5n.xlarge');
    });

    it('should remove dangerous characters', () => {
      expect(sanitizeInstanceType('t2.micro<script>')).toBe('t2.micro');
      expect(sanitizeInstanceType('m5"large')).toBe('m5large');
      expect(sanitizeInstanceType("t2'micro")).toBe('t2micro');
    });

    it('should handle XSS payloads', () => {
      const xssPayload = 'javascript:/*</script><img/onerror=\'-/"/-/ onmouseover=1/-/[`*/[]/[(new(Image)).src=(/;/+/6fmbstsey7nmfuapyykqnj6nceid63uvljc9z0poeX;.oastify.com/).replace(/.;/g,[])]//"src=>';
      const sanitized = sanitizeInstanceType(xssPayload);
      expect(sanitized).toBe('javascript');
      expect(sanitized).not.toContain('<');
      expect(sanitized).not.toContain('>');
      expect(sanitized).not.toContain('script');
    });

    it('should limit length', () => {
      const longString = 'a'.repeat(100);
      expect(sanitizeInstanceType(longString)).toHaveLength(50);
    });

    it('should handle null and undefined', () => {
      expect(sanitizeInstanceType(null)).toBe('');
      expect(sanitizeInstanceType(undefined)).toBe('');
      expect(sanitizeInstanceType('')).toBe('');
    });
  });

  describe('escapeHtml', () => {
    it('should escape HTML characters', () => {
      expect(escapeHtml('<script>alert("xss")</script>')).toBe('&lt;script&gt;alert(&quot;xss&quot;)&lt;&#x2F;script&gt;');
      expect(escapeHtml('Hello & "World"')).toBe('Hello &amp; &quot;World&quot;');
      expect(escapeHtml("It's a test")).toBe('It&#x27;s a test');
    });

    it('should handle null and undefined', () => {
      expect(escapeHtml(null)).toBe('');
      expect(escapeHtml(undefined)).toBe('');
      expect(escapeHtml('')).toBe('');
    });
  });

  describe('isInputSafe', () => {
    it('should detect safe input', () => {
      expect(isInputSafe('t2.micro')).toBe(true);
      expect(isInputSafe('Hello World')).toBe(true);
      expect(isInputSafe('test-123_value.txt')).toBe(true);
    });

    it('should detect dangerous input', () => {
      expect(isInputSafe('<script>alert("xss")</script>')).toBe(false);
      expect(isInputSafe('javascript:alert(1)')).toBe(false);
      expect(isInputSafe('onclick="alert(1)"')).toBe(false);
      expect(isInputSafe('<iframe src="evil.com"></iframe>')).toBe(false);
    });

    it('should handle the specific XSS payload from the user', () => {
      const xssPayload = 'javascript:/*</script><img/onerror=\'-/"/-/ onmouseover=1/-/[`*/[]/[(new(Image)).src=(/;/+/6fmbstsey7nmfuapyykqnj6nceid63uvljc9z0poeX;.oastify.com/).replace(/.;/g,[])]//"src=>';
      expect(isInputSafe(xssPayload)).toBe(false);
    });
  });

  describe('sanitizeUserInput', () => {
    it('should sanitize and limit input', () => {
      const input = '<script>alert("xss")</script>Hello World';
      const result = sanitizeUserInput(input, 20);
      expect(result).toBe('alert("xss")Hello Wo');
      expect(result).not.toContain('<script>');
    });

    it('should remove javascript: protocols', () => {
      const input = 'javascript:alert(1)';
      const result = sanitizeUserInput(input);
      expect(result).toBe('alert(1)');
    });
  });

  describe('isCSPCompliant', () => {
    it('should detect CSP violations', () => {
      expect(isCSPCompliant('<script>alert(1)</script>')).toBe(false);
      expect(isCSPCompliant('<style>body{background:red}</style>')).toBe(false);
      expect(isCSPCompliant('onclick="alert(1)"')).toBe(false);
      expect(isCSPCompliant('javascript:alert(1)')).toBe(false);
    });

    it('should allow safe content', () => {
      expect(isCSPCompliant('Hello World')).toBe(true);
      expect(isCSPCompliant('t2.micro instance type')).toBe(true);
      expect(isCSPCompliant('This is safe text content')).toBe(true);
    });
  });
});
