#!/bin/bash

# Deploy Engage V2 UI to S3 with CloudFront invalidation
# Usage: ./deploy-to-s3.sh <bucket-name> <cloudfront-distribution-id> [environment]

set -e

# Configuration
BUCKET_NAME=${1:-"engage-v2-ui-bucket"}
CLOUDFRONT_DISTRIBUTION_ID=${2:-""}
ENVIRONMENT=${3:-"production"}
BUILD_DIR="dist"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting deployment to S3...${NC}"

# Check if AWS CLI is installed
if ! command -v aws &> /dev/null; then
    echo -e "${RED}❌ AWS CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if build directory exists
if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}❌ Build directory '$BUILD_DIR' not found. Please run 'pnpm build' first.${NC}"
    exit 1
fi

# Check if bucket exists
if ! aws s3 ls "s3://$BUCKET_NAME" &> /dev/null; then
    echo -e "${RED}❌ S3 bucket '$BUCKET_NAME' not found or not accessible.${NC}"
    exit 1
fi

echo -e "${YELLOW}📦 Deploying to bucket: $BUCKET_NAME${NC}"
echo -e "${YELLOW}🌍 Environment: $ENVIRONMENT${NC}"

# Sync files to S3 with appropriate cache headers
echo -e "${GREEN}📤 Uploading files to S3...${NC}"

# Upload HTML files with no-cache headers (for SPA routing)
aws s3 sync "$BUILD_DIR" "s3://$BUCKET_NAME" \
    --exclude "*" \
    --include "*.html" \
    --cache-control "no-cache, no-store, must-revalidate" \
    --metadata-directive REPLACE \
    --delete

# Upload CSS and JS files with long cache headers
aws s3 sync "$BUILD_DIR" "s3://$BUCKET_NAME" \
    --exclude "*.html" \
    --include "*.css" \
    --include "*.js" \
    --cache-control "public, max-age=********, immutable" \
    --metadata-directive REPLACE

# Upload other assets with medium cache headers
aws s3 sync "$BUILD_DIR" "s3://$BUCKET_NAME" \
    --exclude "*.html" \
    --exclude "*.css" \
    --exclude "*.js" \
    --cache-control "public, max-age=86400" \
    --metadata-directive REPLACE \
    --delete

echo -e "${GREEN}✅ Files uploaded successfully!${NC}"

# Invalidate CloudFront cache if distribution ID is provided
if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
    echo -e "${GREEN}🔄 Invalidating CloudFront cache...${NC}"
    
    INVALIDATION_ID=$(aws cloudfront create-invalidation \
        --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
        --paths "/*" \
        --query 'Invalidation.Id' \
        --output text)
    
    echo -e "${GREEN}✅ CloudFront invalidation created: $INVALIDATION_ID${NC}"
    echo -e "${YELLOW}⏳ Waiting for invalidation to complete...${NC}"
    
    aws cloudfront wait invalidation-completed \
        --distribution-id "$CLOUDFRONT_DISTRIBUTION_ID" \
        --id "$INVALIDATION_ID"
    
    echo -e "${GREEN}✅ CloudFront invalidation completed!${NC}"
else
    echo -e "${YELLOW}⚠️  No CloudFront distribution ID provided. Skipping cache invalidation.${NC}"
fi

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"

# Output useful information
echo -e "${YELLOW}📋 Deployment Summary:${NC}"
echo -e "   Bucket: s3://$BUCKET_NAME"
echo -e "   Environment: $ENVIRONMENT"
if [ -n "$CLOUDFRONT_DISTRIBUTION_ID" ]; then
    echo -e "   CloudFront Distribution: $CLOUDFRONT_DISTRIBUTION_ID"
fi
echo -e "   Build Directory: $BUILD_DIR"
